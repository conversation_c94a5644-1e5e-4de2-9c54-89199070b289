// pages/goods/my/my.js
const util = require('@/utils/util.js')
const dateUtil = require('@/utils/dateUtil.js')
const goodsApi = require('@/api/goods.js')
const commApi = require('@/api/commApi.js')

Page({
  data: {
    darkMode: false,
    activeTab: 'published', // published, orders, favorites
    publishedGoods: [],
    orders: [],
    favorites: [],
    userInfo: null,
    apiUrl: '', // 图片访问路径
    statusOptions: [], // 商品状态字典
    typeOptions: [], // 商品类型字典
    categoryOptions: [], // 商品分类字典
    orderStatusOptions: [], // 订单状态字典
    orderIdentityOptions: [], // 订单身份字典（我的购买/我卖出的）
    currentOrderIdentity: 'buy', // 当前选择的订单身份，默认我的购买
    showCancelModal: false, // 是否显示取消原因选择弹窗
    cancelOrderId:null,
    cancelReasons: [ // 取消原因选项（前三个是单选项）
      { value: 'wrong_order', text: '拍错了' },
      { value: 'no_need', text: '不想要了' },
      { value: 'price_issue', text: '价格问题' }
    ],
    selectedCancelReason: '',
    customCancelReason: ''
  },

  onLoad: function (options) {
    // 初始化图片访问路径
    this.setData({
      apiUrl: wx.getStorageSync('apiUrl') + '/common-api/v1/file/'
    });

    // 如果有传入的tab参数，则切换到对应的tab
    if (options.tab && ['published', 'orders', 'favorites'].includes(options.tab)) {
      this.setData({
        activeTab: options.tab
      });

      // 如果是订单tab，确保默认选择第一个字典项
      if (options.tab === 'orders') {
        // 这里先设置默认值，字典加载完成后会在loadDictionaries中更新
        this.setData({
          currentOrderIdentity: 'buy'
        });
      }
    }

    // 加载字典数据
    this.loadDictionaries();

    // 获取用户信息
    this.getUserInfo()


    // 加载数据
    this.loadData()
  },

  // 加载字典数据
  loadDictionaries: function () {
    try {
      // 使用统一的字典获取方法
      const statusDict = util.getDictByNameEn('good_stuff_status');
      const typeDict = util.getDictByNameEn('good_stuff_type');
      const categoryDict = util.getDictByNameEn('good_stuff_category');

      const orderStatusDict = util.getDictByNameEn('good_stuff_order_status');

      const orderIdentityDict = util.getDictByNameEn('goods_order_identities');

      // 设置订单身份选项，优先使用字典数据
      let orderIdentityOptions = [];
      if (orderIdentityDict && orderIdentityDict.length > 0 && orderIdentityDict[0].children) {
        orderIdentityOptions = orderIdentityDict[0].children;
      } else {
        // 字典为空时使用默认值
        orderIdentityOptions = [
          { nameCn: '我的购买', nameEn: 'buy' },
          { nameCn: '我卖出的', nameEn: 'sell' }
        ];
      }

      // 设置默认选中第一个字典项
      let defaultOrderIdentity = 'buy';
      if (orderIdentityOptions.length > 0) {
        defaultOrderIdentity = orderIdentityOptions[0].nameEn;
      }

      this.setData({
        statusOptions: statusDict && statusDict.length > 0 && statusDict[0].children ? statusDict[0].children : [],
        typeOptions: typeDict && typeDict.length > 0 && typeDict[0].children ? typeDict[0].children : [],
        categoryOptions: categoryDict && categoryDict.length > 0 && categoryDict[0].children ? categoryDict[0].children : [],
        orderStatusOptions: orderStatusDict && orderStatusDict.length > 0 && orderStatusDict[0].children ? orderStatusDict[0].children : [],
        orderIdentityOptions: orderIdentityOptions,
        currentOrderIdentity: defaultOrderIdentity // 设置为第一个字典项
      });

      console.log('字典数据加载完成:', {
        statusOptions: this.data.statusOptions,
        typeOptions: this.data.typeOptions,
        categoryOptions: this.data.categoryOptions,
        orderStatusOptions: this.data.orderStatusOptions,
        orderIdentityOptions: this.data.orderIdentityOptions
      });
    } catch (error) {
      console.error('加载字典数据失败:', error);
      // 设置空数组作为默认值
      this.setData({
        statusOptions: [],
        typeOptions: [],
        categoryOptions: [],
        orderStatusOptions: [],
        orderIdentityOptions: []
      });
    }
  },

  onShow: function () {
    // 每次显示页面时刷新数据
    this.loadData()
  },

  onPullDownRefresh: function () {
    // 下拉刷新
    this.loadData().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 切换标签页
  switchTab: function (e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({
      activeTab: tab
    })

    // 如果数据为空，则加载数据
    if (tab === 'published' && this.data.publishedGoods.length === 0) {
      this.loadPublishedGoods()
    } else if (tab === 'orders') {
      // 切换到我的订单时，确保选择第一个字典项，并重新加载数据
      let firstIdentity = 'buy'; // 默认值
      if (this.data.orderIdentityOptions.length > 0) {
        firstIdentity = this.data.orderIdentityOptions[0].nameEn;
      }
      this.setData({
        currentOrderIdentity: firstIdentity
      });
      this.loadOrders()
    } else if (tab === 'favorites' && this.data.favorites.length === 0) {
      this.loadFavorites()
    }
  },

  // 加载所有数据
  loadData: function () {
    return Promise.all([
      this.loadPublishedGoods(),
      this.loadOrders(),
      this.loadFavorites()
    ])
  },

  // 获取用户信息
  getUserInfo: function () {
    // 从本地存储获取用户信息
    const userInfo = wx.getStorageSync('userInfo') || {};
    this.setData({
      userInfo: userInfo
    });
  },

  // 加载我发布的商品
  loadPublishedGoods: function () {

    var params={
      pageNum: 1,
      pageSize: 100
    }
    return goodsApi.getMyGoodsList(params).then(res => {
      console.log('获取我的发布商品:', res);

      if (res && res.list) {
        const publishedGoods = res.list.map(item => {
          const goods = {
            id: item.id,
            title: item.title || '',
            stuffDescribe: item.stuffDescribe || '',
            amount: item.amount || 0,
            media: item.media || '', // 保持原始media字段
            status: item.status || 'pending', // 保持原始状态值
            statusName: this.getGoodsStatus(item.status), // 状态显示名称
            statusClass: this.getGoodsStatusClass(item.status), // 状态样式类名
            examineNote: item.examineNote || '', // 审核原因
            stock: item.stock || 0,
            soldStock: item.soldStock || 0,
            views: item.views || 0,
            createTime: item.createTime ? dateUtil.formatTime(new Date(item.createTime)) : '',
            type: item.type || 'free',
            typeName: this.getGoodsTypeName(item.type), // 类型显示名称
            isFreeType: this.checkIsFreeType(item.type), // 动态判断是否免费类型
            categoryCode: item.categoryCode || '',
            categoryName: this.getGoodsCategoryName(item.categoryCode), // 分类显示名称
            address: item.address || '',
            pendingOrders: 0 // 待核销订单数，需要单独接口获取
          };

          // 处理图片URL
          if (goods.media) {
            goods.images = goods.media.split(',').map(image => {
              const trimmedImage = image.trim();
              if (trimmedImage && !trimmedImage.startsWith('http')) {
                return this.data.apiUrl + trimmedImage;
              }
              return trimmedImage;
            });
          } else {
            goods.images = [];
          }

          return goods;
        });





        this.setData({
          publishedGoods
        });
      } else {
        console.warn('获取我的发布商品失败:', res.message);
        this.setData({
          publishedGoods: []
        });
      }
    }).catch(err => {
      console.error('加载我发布的商品失败:', err);

    
      this.setData({
        publishedGoods: []
      });
    });
  },

  // 加载我的订单
  loadOrders: function () {
    return goodsApi.getMyGoodsOrderList({
      pageNum: 1,
      pageSize: 100,
      searchType: this.data.currentOrderIdentity // 使用当前选择的身份
    }).then(res => {
      console.log('获取我的订单:', res);
      if ( res && res.list) {
        const orders = res.list.map(order => {
          // 解析 stuffSnapshot 获取商品信息
          let goodsInfo = {};
          if (order.stuffSnapshot) {
            try {
              goodsInfo = JSON.parse(order.stuffSnapshot);
            } catch (e) {
              console.error('解析 stuffSnapshot 失败:', e);
              goodsInfo = {};
            }
          }

          const orderItem = {
            id: order.id,
            orderNo: order.orderNo || '',
            goodsId: goodsInfo.id || order.goodStuffId,
            stuffDescribe: goodsInfo.stuffDescribe || order.goodStuffDescribe || '',
            amount: order.unitAmount || goodsInfo.amount || 0,
            totalAmount: order.totalAmount || 0,
            unitAmount: order.unitAmount || 0,
            image: goodsInfo.media ? goodsInfo.media.split(',')[0] : '',
            quantity: order.quantity || 1,
            status: order.status || 'pending',
            statusName: this.getOrderStatusName(order.status),
            createTime: order.createTime ? dateUtil.formatTime(new Date(order.createTime)) : '',
            updateTime: order.updateTime ? dateUtil.formatTime(new Date(order.updateTime)) : '',
            sellerName: goodsInfo.userName || order.sellerName || '卖家',
            sellerId: order.sellerId || goodsInfo.userId,
            phone: order.phone || '',
            note: order.note || '',
            type: goodsInfo.type || order.goodStuffType || 'free',
            typeName: this.getGoodsTypeName(goodsInfo.type || order.goodStuffType),
            isFreeType: this.checkIsFreeType(goodsInfo.type || order.goodStuffType),
            address: goodsInfo.address || '',
            // 保存原始快照数据
            stuffSnapshot: goodsInfo
          };

          // 处理图片URL
          if (orderItem.image) {

            orderItem.medias = orderItem.image.split(',').map(image => {

              const trimmedImage = image.trim();
              if (trimmedImage && !trimmedImage.startsWith('http')) {
                return this.data.apiUrl + trimmedImage;
              }
              return trimmedImage;
            });
          } else {
            orderItem.medias = [];
          }
          return orderItem;
        });

        this.setData({
          orders
        });
      } else {
        console.warn('获取我的订单失败:', res.message);
        this.setData({
          orders: []
        });
      }
    }).catch(err => {
      console.error('加载我的订单失败:', err);
 
      this.setData({
        orders: []
      });
    });
  },

  // 获取商品状态显示文本
  getGoodsStatus: function (status) {
    const statusOption = this.data.statusOptions.find(option => option.nameEn === status);
    return statusOption ? statusOption.nameCn : status;
  },

  // 获取商品状态样式类名
  getGoodsStatusClass: function (status) {
    switch (status) {
      case 'list': return 'on_shelf';    // 已上架
      case 'pending': return 'pending';    // 待审核
      case 'unlist': return 'off_shelf';   // 已下架
      default: return 'off_shelf';
    }
  },

  // 获取商品类型显示名称
  getGoodsTypeName: function (type) {
    const typeOption = this.data.typeOptions.find(option => option.nameEn === type);
    return typeOption ? typeOption.nameCn : type;
  },

  // 获取商品分类显示名称
  getGoodsCategoryName: function (categoryCode) {
    const categoryOption = this.data.categoryOptions.find(option => option.nameEn === categoryCode);
    return categoryOption ? categoryOption.nameCn : categoryCode;
  },

  // 动态判断是否为免费类型（基于字典配置）
  checkIsFreeType: function (type) {
    const typeOption = this.data.typeOptions.find(option => option.nameEn === type);
    // 可以根据字典中的特殊标识来判断，比如 nameEn 包含 'free' 或者有特殊字段
    return typeOption && (typeOption.nameEn.includes('free') || typeOption.nameCn.includes('免费'));
  },

  // 获取订单状态显示名称（基于字典）
  getOrderStatusName: function (status) {
    const statusOption = this.data.orderStatusOptions.find(option => option.nameEn === status);
    return statusOption ? statusOption.nameCn : status;
  },

  // 获取订单状态样式类名
  getOrderStatusClass: function (status) {
    const statusOption = this.data.orderStatusOptions.find(option => option.nameEn === status);
    if (statusOption) {
      // 根据字典中的状态返回对应的样式类
      switch (status) {
        case 'pending': return 'pending';
        case 'wait_complete': return 'processing';
        case 'complete': return 'completed';
        case 'cancel': return 'cancelled';
        default: return 'pending';
      }
    }
    return 'pending';
  },

  // 切换订单身份（买家/卖家）
  switchOrderIdentity: function (e) {
    const identity = e.currentTarget.dataset.identity;
    this.setData({
      currentOrderIdentity: identity
    });
    // 重新加载订单数据
    this.loadOrders();
  },

  // 加载我的收藏
  loadFavorites: function () {
    return goodsApi.getMyCollectedGoodsList({
      pageNum: 1,
      pageSize: 100
    }).then(res => {
      console.log('获取我的收藏:', res);

      if (res && res.list) {
        const favorites = res.list.map(item => {

          const favoriteItem = {
            id: item.id,
            goodsId: item.goodStuffId,
            stuffDescribe: item.goodStuffDescribe || '',
            amount: item.amount || 0,
            image: item.media || '',
            memberName: item.goodStuffMemberName || '用户',
            favoriteTime: item.createTime ? dateUtil.formatTime(new Date(item.createTime)) : '',
            type: item.goodStuffType || 'free',
            typeName: this.getGoodsTypeName(item.goodStuffType),
            isFreeType: this.checkIsFreeType(item.goodStuffType)
          };
           
          // 处理图片URL
          if (favoriteItem.image) {
            favoriteItem.images = favoriteItem.image.split(',').map(image => {

              const trimmedImage = image.trim();
              if (trimmedImage && !trimmedImage.startsWith('http')) {
                return this.data.apiUrl + trimmedImage;
              }
              return trimmedImage;
            });
          } else {
            favoriteItem.images = [];
          }

          return favoriteItem;
        });



         
        this.setData({
          favorites
        });
      } else {
        console.warn('获取我的收藏失败:', res.message);
        this.setData({
          favorites: []
        });
      }
    }).catch(err => {
      console.error('加载我的收藏失败:', err);
   
      this.setData({
        favorites: []
      });
    });
  },

  // 导航到商品详情
  navigateToDetail: function (e) {
     
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/profilePackage/pages/goods/detail/detail?id=${id}`
    })
  },

  // 导航到发布页面
  navigateToPublish: function () {
    // 检查是否已认证
    if (util.checkAuthentication()) {
      wx.navigateTo({
        url: '/profilePackage/pages/goods/publish/publish'
      })
    } else {
      util.showAuthModal()
    }
  },

  // 导航到好物列表页面
  navigateToGoods: function () {
    wx.switchTab({
      url: '/pages/goods/goods'
    })
  },

  // 编辑商品
  editGoods: function (e) {
    const id = e.currentTarget.dataset.id;
    // 编辑商品时跳转到编辑页面，传递商品ID
    wx.navigateTo({
      url: `/pages/goods/publish/publish?id=${id}&mode=edit`
    });
  },

  // 导航到订单详情
  navigateToOrder: function (e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/profilePackage/pages/goods/order/order?id=${id}&searchType=${this.data.currentOrderIdentity}`
    })
  },

  // 导航到待核销订单列表
  navigateToPendingVerifications: function (e) {
    const goodsId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/profilePackage/pages/goods/verification/list/list?goodsId=${goodsId}`
    })
  },

  // 编辑商品
  editGoods: function (e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/profilePackage/pages/goods/publish/publish?id=${id}&edit=true`
    })
  },


  // 删除商品
  deleteGoods: function (e) {
    const id = e.currentTarget.dataset.id

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个商品吗？',
      success: (res) => {
        if (res.confirm) {
          goodsApi.deleteMyGoods(id).then(res => {
          
              // 更新本地数据
              const publishedGoods = this.data.publishedGoods.filter(item => item.id !== id);

              this.setData({
                publishedGoods
              });

              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
          
          }).catch(err => {
            console.error('删除商品失败:', err);
         
          });
        }
      }
    })
  },

  // 取消收藏
  cancelFavorite: function (e) {
    const id = e.currentTarget.dataset.id

    goodsApi.uncollectGoods(id).then(res => {
    
        // 更新本地数据
        const favorites = this.data.favorites.filter(item => item.id !== id);

        this.setData({
          favorites
        });

        wx.showToast({
          title: '已取消收藏',
          icon: 'success'
        });
    
    }).catch(err => {
      console.error('取消收藏失败:', err);

    });
  },

  // 取消订单
  cancelOrder: function(e) {
    const id = e.currentTarget.dataset.id
     
    // 显示取消原因选择弹窗
    this.showCancelModal(id);
  },

  // 显示取消订单弹窗
  showCancelModal: function(id) {
     
    this.setData({
      cancelOrderId:id,
      showCancelModal: true,
      selectedCancelReason: '',
      customCancelReason: ''
    });
  },
  // 隐藏取消订单弹窗
  hideCancelModal: function() {
    this.setData({
      showCancelModal: false,
      cancelOrderId:null,
    });
  },
  

  // 阻止事件冒泡
  stopPropagation: function() {
    // 空方法，用于阻止事件冒泡
  },

  // 选择取消原因
  selectCancelReason: function(e) {
    const reason = e.currentTarget.dataset.reason;
    console.log('选择预设原因:', reason);
    this.setData({
      selectedCancelReason: reason,
      customCancelReason: '' // 选择预设原因时清空自定义输入
    });
    console.log('当前选择的原因:', this.data.selectedCancelReason);
  },


  // 选择其他原因
  selectOtherReason: function() {
    console.log('选择其他原因');
    this.setData({
      selectedCancelReason: 'other'
    });
    console.log('当前选择的原因:', this.data.selectedCancelReason);
  },

  // 输入自定义取消原因
  inputCustomReason: function(e) {
    this.setData({
      customCancelReason: e.detail.value
    });
  },

  // 确认取消订单
  confirmCancelOrder: function() {
    if (!this.data.selectedCancelReason) {
      wx.showToast({
        title: '请选择取消原因',
        icon: 'none'
      });
      return;
    }

    if (this.data.selectedCancelReason === 'other' && !this.data.customCancelReason.trim()) {
      wx.showToast({
        title: '请输入取消原因',
        icon: 'none'
      });
      return;
    }

    // 获取取消原因文本
    let cancelNote = '';
    if (this.data.selectedCancelReason === 'other') {
      cancelNote = this.data.customCancelReason.trim();
    } else {
      const reasonObj = this.data.cancelReasons.find(r => r.value === this.data.selectedCancelReason);
      cancelNote = reasonObj ? reasonObj.text : '用户取消';
    }

    this.performCancelOrder(cancelNote);
  },

  // 执行取消订单操作
  performCancelOrder: function(note) {
    wx.showLoading({
      title: '处理中...'
    });
     
    // 调用取消订单API
    goodsApi.cancelMyGoodsOrder({
      id: this.data.cancelOrderId,
      note: note
    }).then(res => {
      wx.hideLoading();
    
        this.hideCancelModal()

        wx.showToast({
          title: '取消成功',
          icon: 'success'
        });
   
    }).catch(err => {
      wx.hideLoading();
      console.error('取消订单失败:', err);
   
    });
  },

  // 联系卖家
  contactSeller: function (e) {
    const { id, name } = e.currentTarget.dataset

    wx.showActionSheet({
      itemList: ['发送私信', '拨打电话', '复制微信号'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 发送私信
          wx.navigateTo({
            url: `/pages/messages/chat?targetId=${id}&targetName=${name}`
          })
        } else if (res.tapIndex === 1) {
          // 拨打电话
          wx.makePhoneCall({
            phoneNumber: '13800138000' // 模拟电话号码
          })
        } else if (res.tapIndex === 2) {
          // 复制微信号
          wx.setClipboardData({
            data: 'wxid_example', // 模拟微信号
            success: () => {
              wx.showToast({
                title: '微信号已复制',
                icon: 'success'
              })
            }
          })
        }
      }
    })
  },

  deleteOrder: function (e) {

    var orderId=e.currentTarget.dataset.id

    console.log('orderId',orderId)


    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个订单吗？',
      success: (res) => {
        if (res.confirm) {
          goodsApi.deleteGoodsOrder(orderId).then(res => {
           
              // 更新本地数据
              const orders = this.data.orders.filter(item => item.id !== orderId);

              this.setData({
                orders
              });

              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
        
          }).catch(err => {
            console.error('删除订单失败:', err);
       
          });
        }
      }
    })

  },


  // 预览二维码
  previewQRCode: function (e) {
    const qrCode = e.currentTarget.dataset.qrcode
    wx.previewImage({
      urls: [qrCode],
      current: qrCode
    })
  }
})
