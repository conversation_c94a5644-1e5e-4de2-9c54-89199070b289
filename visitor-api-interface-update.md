# 访客延期和收藏功能API接口更新

## 修改概述
根据要求更新了访客延期和收藏功能的API接口调用，使用专门的接口而不是通用的编辑接口。

## API接口变更

### 1. 延期功能接口更新

#### 原接口
- **接口名**: `visitorsApi.editVisitor(params)`
- **参数**: `{id, visitTime: 延长后的ISO时间}`

#### 新接口
- **接口名**: `visitorsApi.editVisitTime(params)`
- **参数**: 
```javascript
{
  id: 该条数据的id,
  stayDuration: 延期时长(1,2,4), 
  timeUnit: "hour", // 表示小时
  visitTime: 该条数据的当前的visitTime
}
```

#### 实现代码
```javascript
// 延长访客时间
extendVisitor: function (e) {
  const hours = parseInt(e.currentTarget.dataset.hours);
  const { currentVisitorId } = this.data;
  
  // 找到当前访客数据
  const currentVisitor = this.data.visitors.find(visitor => visitor.id === currentVisitorId);
  
  // 准备API参数
  const params = {
    id: currentVisitorId,
    stayDuration: hours,
    timeUnit: "hour",
    visitTime: currentVisitor.originalVisitTime
  };

  // 调用延期访客接口
  visitorsApi.editVisitTime(params)
    .then(res => {
      // 延期成功处理
      this.hideExtendModal();
      wx.showToast({
        title: `已延长${hours}小时`,
        icon: 'success'
      });
      this.loadVisitors(); // 重新加载访客列表
    })
    .catch(err => {
      // 错误处理
    });
}
```

### 2. 收藏功能接口更新

#### 原接口
- **接口名**: `visitorsApi.editVisitor(params)`
- **参数**: `{id, isUsual: true/false}`

#### 新接口
- **接口名**: `visitorsApi.editVisitUse(params)`
- **参数**:
```javascript
{
  id: 该条数据的id,
  isUsual: true或者false
}
```

#### 访客列表页面实现
```javascript
// 切换收藏状态
toggleFavorite: function (e) {
  const id = e.currentTarget.dataset.id;
  const currentVisitor = this.data.visitors.find(visitor => visitor.id === id);
  const newIsUsual = !currentVisitor.isUsual;

  // 准备API参数
  const params = {
    id: id,
    isUsual: newIsUsual
  };

  // 调用设为常用访客接口
  visitorsApi.editVisitUse(params)
    .then(res => {
      // 更新本地数据
      const updatedVisitors = this.data.visitors.map(visitor => {
        if (visitor.id === id) {
          return { ...visitor, isUsual: newIsUsual };
        }
        return visitor;
      });

      this.setData({ visitors: updatedVisitors });
      this.applyFilterAndSearch();

      wx.showToast({
        title: newIsUsual ? '已收藏' : '已取消收藏',
        icon: 'success'
      });
    })
    .catch(err => {
      // 错误处理
    });
}
```

#### 访客凭证页面实现
```javascript
// 切换收藏状态
toggleFavorite: function () {
  const { visitorData } = this.data;
  const newIsUsual = !visitorData.isUsual;

  // 准备API参数
  const params = {
    id: visitorData.id,
    isUsual: newIsUsual
  };

  // 调用设为常用访客接口
  visitorsApi.editVisitUse(params)
    .then(res => {
      // 更新本地数据
      this.setData({
        'visitorData.isUsual': newIsUsual
      });

      wx.showToast({
        title: newIsUsual ? '已收藏' : '已取消收藏',
        icon: 'success'
      });
    })
    .catch(err => {
      // 错误处理
    });
}
```

## 接口参数详解

### editVisitTime 接口参数
| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| id | String | 访客数据的唯一标识 | "12345" |
| stayDuration | Number | 延期时长 | 1, 2, 4 |
| timeUnit | String | 时间单位，固定为"hour" | "hour" |
| visitTime | String | 当前访客的访问时间(ISO格式) | "2025-06-22T10:00:00.000Z" |

### editVisitUse 接口参数
| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| id | String | 访客数据的唯一标识 | "12345" |
| isUsual | Boolean | 是否设为常用访客 | true, false |

## 功能特点

### 延期功能
1. **参数简化**: 不需要计算具体的延长时间，只需传递延期时长
2. **服务端计算**: 服务端根据当前时间和延期时长计算新的结束时间
3. **时间单位**: 支持小时为单位的延期操作
4. **原始时间**: 基于访客的原始访问时间进行延期

### 收藏功能
1. **专用接口**: 使用专门的常用访客设置接口
2. **状态切换**: 支持收藏和取消收藏的切换操作
3. **即时反馈**: 接口成功后立即更新UI状态
4. **多页面同步**: 列表页和详情页的收藏状态保持同步

## 错误处理

### 通用错误处理
- 显示加载状态防止重复操作
- 接口失败时显示错误提示
- 成功后显示操作结果提示
- 失败时不更新本地状态

### 数据验证
- 检查访客数据是否存在
- 验证必要参数的完整性
- 确保ID的有效性

## 用户体验优化

### 延期操作
1. **加载提示**: 显示"延期中..."加载状态
2. **成功反馈**: 显示"已延长X小时"提示
3. **数据刷新**: 延期成功后重新加载访客列表
4. **弹窗关闭**: 操作成功后自动关闭延期弹窗

### 收藏操作
1. **即时响应**: 点击后立即更新图标状态
2. **状态提示**: 显示"已收藏"或"已取消收藏"
3. **视觉反馈**: 收藏图标颜色变化
4. **数据同步**: 本地状态与服务器保持一致

## 测试验证

### 延期功能测试
1. 选择待到访状态的访客
2. 点击延期按钮打开弹窗
3. 选择不同的延期时长(1小时、2小时、4小时)
4. 验证API调用参数正确性
5. 确认延期成功后数据更新

### 收藏功能测试
1. 点击未收藏访客的星形图标
2. 验证API调用和参数传递
3. 确认图标状态变化(灰色→黄色)
4. 再次点击验证取消收藏功能
5. 检查不同页面间状态同步

这次更新使用了更专业和规范的API接口，提高了功能的可靠性和维护性。
