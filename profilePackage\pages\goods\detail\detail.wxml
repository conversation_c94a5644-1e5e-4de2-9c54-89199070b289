<!--商品详情页-->
<view class="container {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}">
  <!-- 商品图片轮播 -->
  <swiper class="goods-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="5000" duration="500" circular="{{true}}">
    <block wx:if="{{goods && goods.images && goods.images.length > 0}}">
      <swiper-item wx:for="{{goods.images}}" wx:key="index">
        <image
          class="goods-swiper-image"
          src="{{item}}"
          mode="aspectFit"
          lazy-load="true"
          binderror="onImageError"
          data-index="{{index}}"
          bindtap="previewImage"
          data-src="{{item}}"
        ></image>
      </swiper-item>
    </block>
    <block wx:else>
      <swiper-item>
        <image class="goods-swiper-image" src="/images/placeholder.png" mode="aspectFit" binderror="onImageError" data-index="0"></image>
      </swiper-item>
    </block>
  </swiper>

  <!-- 商品基本信息 -->
  <view class="goods-info">
    <view class="goods-price-row">
      <block wx:if="{{goods}}">
        <view class="goods-price" wx:if="{{!goods.isFreeType}}">¥{{goods.amount || '0.00'}}</view>
        <view class="goods-free" wx:else>{{goods.typeName}}送</view>
        <view class="goods-original-price" wx:if="{{goods.originalPrice && !goods.isFreeType}}">¥{{goods.originalPrice}}</view>
      </block>
      <block wx:else>
        <view class="goods-price">¥0.00</view>
      </block>
      <view class="goods-type">{{goods.typeName}}</view>
    </view>
    <view class="goods-title">{{goods.stuffDescribe || '商品标题'}}</view>
    <!-- 交易地点 -->
    <view class="goods-address" wx:if="{{goods.address}}">
      <text class="address-icon">📍</text>
      <text class="address-text">{{goods.address}}</text>
    </view>
    <view class="goods-stats">
      <view class="goods-stat-item">
        <text class="stat-label">库存</text>
        <text class="stat-value">{{goods.stock || 0}}</text>
      </view>
      <view class="goods-stat-item">
        <text class="stat-label">已售</text>
        <text class="stat-value">{{goods.sold || 0}}</text>
      </view>
      <view class="goods-stat-item">
        <text class="stat-label">浏览</text>
        <text class="stat-value">{{goods.views || 0}}</text>
      </view>
      <view class="goods-stat-item" wx:if="{{goods.statusName}}">
        <text class="stat-label">状态</text>
        <text class="stat-value">{{goods.statusName}}</text>
      </view>
    </view>

    <!-- 积分信息展示 - 暂时隐藏 -->
    <view class="points-info-section" wx:if="{{false && goods.type !== 'free'}}">
      <view class="points-reward">
        <text class="points-tag">积分奖励</text>
        <text>购买可得 {{estimatedRewardPoints || (goods.amount ? Math.floor(goods.amount * (goods.pointsReward || 1)) : 0)}} 积分</text>
      </view>
      <view class="points-deduction" wx:if="{{displayMaxDeductiblePoints > 0}}">
        <text class="points-tag">积分抵扣</text>
        <text>可用 {{displayMaxDeductiblePoints}} 积分 抵扣 ¥{{displayDeductibleAmount}}</text>
      </view>
    </view>
  </view>

  <!-- 卖家信息 - 只有非自己发布的商品才显示 -->
  <view class="seller-info" wx:if="{{!goods.isMy}}">
    <image class="seller-avatar" src="{{goods.memberAvatar || '/images/default-avatar.svg'}}"></image>
    <view class="seller-detail">
      <view class="seller-name">{{goods.userName}}</view>
    </view>
    <!-- <button class="contact-btn" bindtap="contactSeller">联系卖家</button> -->
  </view>

  <!-- 积分抵扣 - 暂时隐藏 -->
  <view class="point-discount" wx:if="{{false && goods.type !== 'free' && goods.pointsDiscount && goods.pointsDiscount.enabled}}">
    <view class="section-title">
      <text class="section-icon">🔄</text>
      <text>积分抵扣</text>
      <view class="points-badge">省钱</view>
    </view>
    <view class="point-info">
      <view class="point-info-item">
        <text class="info-label">可用积分:</text>
        <text class="info-value">{{userPoints}}</text>
      </view>
      <view class="point-info-item">
        <text class="info-label">积分比例:</text>
        <text class="info-value">{{goods.pointsDiscount.ratio}}积分 = 1元</text>
      </view>
      <view class="point-info-item">
        <text class="info-label">最高可抵扣:</text>
        <text class="info-value">订单金额的{{pointsRatio || goods.pointsDiscount.maxRatio * 100}}%</text>
      </view>
    </view>
    <view class="point-slider-container">
      <view class="slider-labels">
        <text>0</text>
        <text>{{maxCanUsePoints}}</text>
      </view>
      <slider
        class="point-slider"
        min="0"
        max="{{maxCanUsePoints}}"
        step="1"
        value="{{usePoints}}"
        activeColor="#ff8c00"
        block-size="24"
        block-color="#ff8c00"
        show-value="{{false}}"
        bindchange="onPointSliderChange"
      ></slider>
      <view class="point-value">
        <view class="point-value-text">使用 <text class="highlight">{{usePoints}}</text> 积分</view>
        <view class="point-discount-text">抵扣 <text class="highlight">¥{{pointDiscount}}</text></view>
      </view>
      <view class="point-tips" wx:if="{{usePoints > 0}}">
        使用积分后，实付金额为 <text class="highlight">¥{{actualPrice}}</text>
      </view>
    </view>
  </view>

  <!-- 返还积分 - 暂时隐藏 -->
  <view class="point-reward" wx:if="{{false && goods.type !== 'free' && goods.pointsReward}}">
    <view class="section-title">
      <text class="section-icon">🎁</text>
      <text>购买返积分</text>
      <view class="points-badge reward">赚分</view>
    </view>
    <view class="reward-info">
      <view class="reward-info-item">
        <text class="info-label">购买可获得:</text>
        <text class="info-value highlight">{{rewardPoints || Math.round(goods.amount * goods.pointsReward * buyQuantity)}} 积分</text>
      </view>
      <view class="reward-info-item">
        <text class="info-label">返积分比例:</text>
        <text class="info-value">消费1元返{{goods.pointsReward}}积分</text>
      </view>
      <view class="reward-tips">积分可在积分商城兑换好礼或抵扣其他商品</view>
    </view>
  </view>

  <!-- 商品详情 -->
  <view class="goods-detail">
    <view class="section-title">商品详情</view>
    <!-- Check if there's any detail content -->
    <block wx:if="{{goods.stuffDescribe || goods.content || (goods.detailImages && goods.detailImages.length > 0)}}">
      <!-- 富文本内容渲染 -->
      <rich-text wx:if="{{goods.stuffDescribe && goods.isRichText}}"
                 class="goods-description rich-content"
                 nodes="{{goods.stuffDescribe}}">
      </rich-text>
      <!-- 普通文本内容 -->
      <text wx:elif="{{goods.stuffDescribe}}" class="goods-description">{{goods.stuffDescribe}}</text>
      <text class="detail-content" wx:if="{{goods.content}}">{{goods.content}}</text>
      <view class="detail-images" wx:if="{{goods.detailImages && goods.detailImages.length > 0}}">
        <image
          wx:for="{{goods.detailImages}}"
          wx:key="index"
          src="{{item}}"
          mode="widthFix"
          lazy-load="true"
          binderror="onDetailImageError"
          data-index="{{index}}"
          bindtap="previewDetailImage"
          data-src="{{item}}"
          class="detail-image"
        ></image>
      </view>
    </block>
    <!-- Fallback message -->
    <block wx:else>
      <view class="no-detail-message">暂无详细介绍</view>
    </block>
  </view>

  <!-- 相似商品推荐 -->
  <view class="similar-goods" wx:if="{{goods.similarGoods && goods.similarGoods.length > 0}}">
    <view class="section-title">相似商品</view>
    <scroll-view class="similar-scroll" scroll-x="true" enhanced show-scrollbar="{{false}}">
      <view
        class="similar-item"
        wx:for="{{goods.similarGoods}}"
        wx:key="id"
        bindtap="navigateToDetail"
        data-id="{{item.id}}"
      >
        <image class="similar-image" src="{{item.images[0]}}" mode="aspectFill"></image>
        <view class="similar-title">{{item.stuffDescribe}}</view>
        <view class="similar-price" wx:if="{{!item.isFreeType}}">¥{{item.amount}}</view>
        <view class="similar-free" wx:else>{{item.typeName}}送</view>
      </view>
    </scroll-view>
  </view>

  <!-- 底部操作栏 -->
  <view class="footer">
    <view class="footer-actions">
      <view class="action-item" bindtap="toggleFavorite">
        <image class="action-icon" src="{{goods.isCollect ? '/images/icons/star-filled.svg' : '/images/icons/star.svg'}}"></image>
        <text>收藏</text>
      </view>
      <view class="action-item" bindtap="toggleLike">
        <image class="action-icon" src="{{goods.isLike ? '/images/icons/like-filled.svg' : '/images/icons/like.svg'}}"></image>
        <text>点赞 {{goods.like || 0}}</text>
      </view>
      <view class="action-item" bindtap="shareGoods">
        <image class="action-icon" src="/images/icons/share.svg"></image>
        <text>分享</text>
      </view>
    </view>
    <!-- 非自己发布的商品显示联系和购买按钮 -->
    <view class="footer-btns" wx:if="{{!goods.isMy}}">
      <button class="contact-seller-btn" bindtap="contactSeller">
        <image class="contact-icon" src="/images/icons/message.svg"></image>
        <text>联系卖家</text>
      </button>
      <button
        class="buy-btn {{goods.stock <= 0 ? 'disabled' : ''}}"
        bindtap="buyGoods"
        disabled="{{goods.stock <= 0}}"
      >
        <text>{{goods.stock <= 0 ? '已售罄' : (goods.isFreeType ? goods.typeName + '领取' : '立即购买')}}</text>
      </button>
    </view>

    <!-- 自己发布的商品显示管理按钮 -->
    <view class="footer-btns my-goods-btns" wx:if="{{goods.isMy}}">
      <button class="edit-btn" bindtap="editGoods">
        <text>编辑商品</text>
      </button>
      <button class="manage-btn" bindtap="manageGoods">
        <text>管理商品</text>
      </button>
    </view>
  </view>

  <!-- 联系方式弹窗 -->
  <view class="modal {{showContactModal ? 'show' : ''}}" bindtap="closeContactModal">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-title">联系卖家</view>
      <view class="contact-options">
        <view class="contact-option" wx:if="{{goods.contactType === 1 || goods.contactType === 2}}">
          <view class="option-label">{{goods.contactType === 1 ? '微信号' : '电话'}}</view>
          <view class="option-value">{{goods.contactInfo}}</view>
          <button class="option-btn" bindtap="copyContactInfo">复制</button>
        </view>
        <view class="contact-option" wx:if="{{goods.contactType === 1 || goods.contactType === 2}}">
          <button class="full-btn" bindtap="callPhone" wx:if="{{goods.contactType === 2}}">拨打电话</button>
        </view>
        <view class="contact-option">
          <button class="full-btn" bindtap="sendMessage">发送私信</button>
        </view>
      </view>
      <view class="modal-close" bindtap="closeContactModal">关闭</view>
    </view>
  </view>

  <!-- 购买确认弹窗 -->
  <view class="modal {{showBuyModal ? 'show' : ''}}" bindtap="closeBuyModal">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-title">{{goods.type === 'free' ? '确认领取' : '确认购买'}}</view>
      <view class="buy-info">
        <view class="buy-item">
          <text class="buy-label">商品</text>
          <text class="buy-value">{{goods.stuffDescribe}}</text>
        </view>
        <view class="buy-item">
          <text class="buy-label">单价</text>
          <text class="buy-value">{{!goods.isFreeType ? '¥' + goods.amount : goods.typeName}}</text>
        </view>
        <view class="buy-item">
          <text class="buy-label">数量</text>
          <view class="quantity-control">
            <view class="quantity-btn" bindtap="decreaseQuantity">-</view>
            <input class="quantity-input" type="number" value="{{buyQuantity}}" bindinput="onQuantityInput" />
            <view class="quantity-btn" bindtap="increaseQuantity">+</view>
          </view>
        </view>
        <!-- 积分抵扣选择 - 暂时隐藏 -->
        <view class="buy-item points-switch-item" wx:if="{{false && goods.type !== 'free' && goods.pointsDiscount && goods.pointsDiscount.enabled && maxCanUsePoints > 0}}">
          <text class="buy-label">积分抵扣</text>
          <view class="buy-value">
            <view class="points-rule">
              <text>最多可用 {{maxCanUsePoints}} 积分</text>
              <text>可抵扣 ¥{{pointDiscount}}</text>
            </view>
            <view class="points-slider-container">
              <slider
                min="0"
                max="{{maxCanUsePoints}}"
                step="1"
                value="{{usePoints}}"
                activeColor="#ff8c00"
                block-size="24"
                block-color="#ff8c00"
                show-value="{{false}}"
                bindchange="onPointSliderChange"
              ></slider>
              <view class="points-slider-labels">
                <text>0</text>
                <text>{{maxCanUsePoints}}</text>
              </view>
            </view>
            <view class="points-value">
              <text>使用 {{usePoints}} 积分</text>
              <text wx:if="{{usePoints > 0}}">抵扣 ¥{{pointDiscount}}</text>
            </view>
          </view>
        </view>
        <!-- 只有普通商品(type!=free)才显示积分返还，免费商品(type=free)不显示 - 暂时隐藏 -->
        <view class="buy-item" wx:if="{{false && goods.type !== 'free' && goods.pointsReward}}">
          <text class="buy-label">返还积分</text>
          <text class="buy-value">+{{rewardPoints || (goods.amount * buyQuantity * goods.pointsReward)}} 积分</text>
        </view>
        <view class="buy-item total">
          <text class="buy-label">总计</text>
          <text class="buy-value">{{goods.isFreeType ? goods.typeName : '¥' + totalPrice}}</text>
        </view>
      </view>
      <view class="buy-remark">
        <text class="remark-label">备注</text>
        <textarea class="remark-input" placeholder="选填，请填写备注信息" bindinput="onRemarkInput" value="{{buyRemark}}"></textarea>
      </view>
      <view class="buy-notice">
        <text>提交订单后，请联系卖家进行线下交易</text>
      </view>
      <view class="modal-btns">
        <button class="modal-btn cancel" bindtap="closeBuyModal">取消</button>
        <button class="modal-btn confirm" bindtap="confirmBuy">{{goods.type === 'free' ? '确认领取' : '确认'}}</button>
      </view>
    </view>
  </view>
</view>
