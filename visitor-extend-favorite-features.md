# 访客延期和收藏功能实现

## 功能概述
实现了访客列表页面的延期功能和收藏功能，以及在访客凭证页面添加收藏图标。

## 实现内容

### 1. 访客延期功能 (`servicePackage/pages/visitor/list/index.js`)

#### 功能描述
- 在访客列表页面点击延期按钮，弹出调整访客时间弹窗
- 点击"延长1小时"选项，调用编辑访客接口延长访问时间

#### 实现逻辑
```javascript
// 延长访客时间
extendVisitor: function (e) {
  const hours = parseInt(e.currentTarget.dataset.hours);
  const { currentVisitorId } = this.data;

  // 找到当前访客数据
  const currentVisitor = this.data.visitors.find(visitor => visitor.id === currentVisitorId);
  
  // 计算延长后的访问时间
  const originalVisitTime = new Date(currentVisitor.originalVisitTime);
  const extendedVisitTime = new Date(originalVisitTime.getTime() + hours * 60 * 60 * 1000);

  // 准备API参数
  const params = {
    id: currentVisitorId,
    visitTime: extendedVisitTime.toISOString()
  };

  // 调用编辑访客接口
  visitorsApi.editVisitor(params)
    .then(res => {
      // 延期成功，重新加载访客列表
      this.loadVisitors();
    });
}
```

#### API参数
- `id`: 访客数据的ID
- `visitTime`: 延长时间后的日期（ISO格式）

### 2. 访客收藏功能

#### 2.1 访客列表页面收藏功能

##### UI设计
- 在状态标签（待到访/已到访）右边添加五角星收藏图标
- `isUsual` 为 `null` 或 `false` 时，收藏图标显示为灰色
- `isUsual` 为 `true` 时，收藏图标显示为黄色

##### 实现代码
```javascript
// 切换收藏状态
toggleFavorite: function (e) {
  const id = e.currentTarget.dataset.id;
  const currentVisitor = this.data.visitors.find(visitor => visitor.id === id);
  
  // 切换收藏状态
  const newIsUsual = !currentVisitor.isUsual;

  // 调用编辑访客接口
  visitorsApi.editVisitor({
    id: id,
    isUsual: newIsUsual
  }).then(res => {
    // 更新本地数据
    const updatedVisitors = this.data.visitors.map(visitor => {
      if (visitor.id === id) {
        return { ...visitor, isUsual: newIsUsual };
      }
      return visitor;
    });
    this.setData({ visitors: updatedVisitors });
  });
}
```

##### WXML结构
```xml
<view class="visitor-status">
  <view class="status-tag status-{{item.status}}">{{statusTextMap[item.status]}}</view>
  <view class="favorite-icon {{item.isUsual ? 'active' : ''}}" catchtap="toggleFavorite" data-id="{{item.id}}">
    <image src="/images/icons/star.svg" class="star-icon"></image>
  </view>
</view>
```

#### 2.2 访客凭证页面收藏功能

##### UI设计
- 在访客信息title的右侧添加收藏图标
- 与状态徽章并排显示

##### 实现代码
```javascript
// 切换收藏状态
toggleFavorite: function () {
  const { visitorData } = this.data;
  const newIsUsual = !visitorData.isUsual;

  visitorsApi.editVisitor({
    id: visitorData.id,
    isUsual: newIsUsual
  }).then(res => {
    this.setData({
      'visitorData.isUsual': newIsUsual
    });
  });
}
```

##### WXML结构
```xml
<view class="visitor-card-header">
  <view class="visitor-card-header-left">
    <image src="/images/icons/visitor-info.svg" class="visitor-card-icon" />
    <text class="visitor-card-title">访客信息</text>
  </view>
  <view class="visitor-card-header-right">
    <view class="visitor-status-badge {{visitorData.status}}">
      <text>{{statusText}}</text>
    </view>
    <view class="favorite-icon {{visitorData.isUsual ? 'active' : ''}}" catchtap="toggleFavorite">
      <image src="/images/icons/star.svg" class="star-icon"></image>
    </view>
  </view>
</view>
```

### 3. 样式设计

#### 收藏图标样式
```css
/* 收藏图标样式 */
.favorite-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.favorite-icon .star-icon {
  width: 16px;
  height: 16px;
  filter: grayscale(100%);
  opacity: 0.5;
  transition: all 0.3s ease;
}

.favorite-icon.active .star-icon {
  filter: none;
  opacity: 1;
  color: #ffd700;
}

.favorite-icon:active {
  transform: scale(0.9);
}
```

#### 访客状态区域样式
```css
.visitor-status {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  min-width: 80px;
}
```

### 4. 数据处理

#### 访客数据结构更新
在访客数据处理中添加 `isUsual` 字段：

```javascript
return {
  id: visitor.id,
  visitorName: visitor.visitorName,
  // ... 其他字段
  isUsual: visitor.isUsual || false, // 是否收藏
  // ... 其他字段
}
```

### 5. API接口

#### 编辑访客接口
- **接口**: `visitorsApi.editVisitor(params)`
- **延期参数**: `{id: 访客ID, visitTime: 延长后的时间}`
- **收藏参数**: `{id: 访客ID, isUsual: true/false}`

### 6. 功能特点

#### 延期功能
1. **简单操作**: 点击延期按钮 → 选择延长时间 → 自动调用API
2. **时间计算**: 基于原始访问时间计算延长后的时间
3. **实时更新**: 延期成功后立即刷新访客列表
4. **用户反馈**: 显示加载状态和成功提示

#### 收藏功能
1. **视觉反馈**: 灰色/黄色状态清晰区分收藏状态
2. **即时响应**: 点击后立即更新UI状态
3. **数据同步**: 本地状态与服务器数据保持同步
4. **多页面支持**: 列表页和详情页都支持收藏操作

### 7. 用户体验

#### 交互设计
- **直观操作**: 五角星图标符合用户习惯
- **状态清晰**: 颜色变化明确表示收藏状态
- **响应及时**: 点击后立即给出视觉反馈
- **操作便捷**: 一键切换收藏状态

#### 布局优化
- **合理排列**: 状态标签和收藏图标并排显示
- **空间利用**: 充分利用header右侧空间
- **视觉平衡**: 左右布局保持视觉平衡

### 8. 测试场景

#### 延期功能测试
1. 选择待到访状态的访客
2. 点击延期按钮
3. 选择"延长1小时"
4. 验证API调用和数据更新

#### 收藏功能测试
1. 点击未收藏访客的星形图标
2. 验证图标变为黄色
3. 再次点击验证取消收藏
4. 检查不同页面间的状态同步

这些功能完善了访客管理系统的用户体验，提供了便捷的延期和收藏操作。
