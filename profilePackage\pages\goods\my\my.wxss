/* 我的好物页面样式 */
.container {
  padding: 0;
  height: 100%;
  background: white;
}

/* 用户信息样式已移除 */

/* 主标签页样式 - 仿照"全部订单"样式 */
.tabs {
  display: flex;
  background: white;
  padding: 0 30rpx;
  margin-bottom: 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.tab {
  margin-right: 60rpx;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #333;
  position: relative;
  font-weight: 500;
}

.tab:last-child {
  margin-right: 0;
}

.tab.active {
  color: #ff8c00;
  font-weight: 600;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: -1rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #ff8c00;
  border-radius: 2rpx;
}

/* 标签页内容区域 */
.tab-content {
  padding: 10rpx;

  min-height: calc(100vh - 200rpx);
}

/* 我的发布样式 */
.goods-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.goods-item {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 20rpx;
  position: relative;
}

.goods-content {
  display: flex;
  padding: 20rpx;
}

.goods-image {
  width: 200rpx;
  height: 200rpx;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.goods-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-right: 80rpx; /* 为右上角状态留出空间 */
}

.goods-status-tag {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
  background-color: #f5f5f5;
  color: #666;
  z-index: 2;
}

.goods-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
}

.goods-price {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff8c00;
  margin-bottom: 12rpx;
}

.goods-free {
  font-size: 32rpx;
  font-weight: 600;
  color: #4caf50;
  margin-bottom: 12rpx;
}

.goods-meta {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 12rpx;
}

.goods-time {
  position: absolute;
  top: 60rpx;
  right: 20rpx;
  font-size: 22rpx;
  color: #999;
  z-index: 1;
}

/* 审核不通过原因显示 */
.goods-reject-reason {
  position: absolute;
  top: 50rpx;
  left: 20rpx;
  right: 20rpx;
  background: #ffebee;
  border: 1rpx solid #f44336;
  border-radius: 8rpx;
  padding: 12rpx;
  display: flex;
  align-items: center;
  z-index: 3;
}

.reject-icon {
  width: 32rpx;
  height: 32rpx;
  background: #f44336;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.reject-text {
  font-size: 24rpx;
  color: #f44336;
  line-height: 1.4;
  flex: 1;
}

.goods-status {
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
}

.goods-status-tag.on_shelf {
  color: #4caf50;
  background-color: #e8f5e9;
}

.goods-status-tag.off_shelf {
  color: #ff9800;
  background-color: #fff3e0;
}

.goods-status-tag.sold_out {
  color: #f44336;
  background-color: #ffebee;
}

.goods-tag-row {
  display: flex;
  gap: 12rpx;
  margin-bottom: 12rpx;
}

.goods-type-tag {
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  background-color: #e3f2fd;
  color: #2196f3;
}

.goods-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  padding: 20rpx;
  font-size: 24rpx;
  color: #666;
  background: #f9f9f9;
  border-top: 1rpx solid #f0f0f0;
}

.goods-stat {
  flex: 1;
  min-width: 120rpx;
  text-align: center;
}

/* 审核原因样式 */
.examine-note {
  padding: 20rpx;
  background-color: #fff2f0;
  border-left: 6rpx solid #ff4d4f;
  margin: 0 20rpx;
  border-radius: 8rpx;
}

.note-text {
  color: #ff4d4f;
  font-size: 24rpx;
  line-height: 1.5;
}

.goods-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
  padding: 20rpx;
  border-top: 1rpx solid #f5f5f5;
  background: #fafafa;
}

.action-btn {
  padding: 8rpx 20rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  background-color: #f5f5f5;
  color: #666;
}

.action-btn.edit {
  background-color: #e3f2fd;
  color: #2196f3;
}

.action-btn.on-shelf {
  background-color: #e8f5e9;
  color: #4caf50;
}

.action-btn.off-shelf {
  background-color: #fff3e0;
  color: #ff9800;
}

.action-btn.delete {
  background-color: #ffebee;
  color: #f44336;
}

.action-btn.verify {
  background-color: #e8f5e9;
  color: #4caf50;
}

/* 我的订单样式 */
/* 订单身份选择器 - 仿照"全部、待付款、待发货"小标签样式 */
.order-identity-selector {
  background: white;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
}

.identity-tabs {
  display: flex;
  gap: 30rpx;
}

.identity-tab {
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  color: #666;
  background: #f8f8f8;
  border-radius: 20rpx;
  border: 1rpx solid transparent;
  font-weight: 400;
}

.identity-tab.active {
  color: #ff8c00;
  background: #fff3e0;
  border-color: #ff8c00;
  font-weight: 500;
}

.order-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.order-item {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.order-header {
  display: flex;
  justify-content: space-between;
  padding: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.order-id {
  font-size: 24rpx;
  color: #666;
}

.order-status {
  font-size: 24rpx;
  font-weight: 500;
}

.order-status.pending_pickup {
  color: #ff9800;
}

.order-status.completed {
  color: #4caf50;
}

.order-status.cancelled {
  color: #f44336;
}

.order-content {
  display: flex;
  padding: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.order-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}

.order-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.order-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.order-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff8c00;
  margin-bottom: 12rpx;
}

.order-meta {
  display: flex;
  flex-direction: column;
  font-size: 24rpx;
  color: #999;
  margin-top: auto;
}
.order-time{
  position: absolute;
  right: 50rpx;
}


.order-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
  padding: 20rpx;
}

.order-actions .action-btn {
  padding: 8rpx 20rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.action-btn.contact {
  background-color: #e3f2fd;
  color: #2196f3;
}

.action-btn.qrcode {
  background-color: #e8f5e9;
  color: #4caf50;
}

.action-btn.cancel {
  background-color: #ffebee;
  color: #f44336;
}

/* 我的收藏样式 */
.favorite-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.favorite-item {
  display: flex;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.favorite-image {
  width: 200rpx;
  height: 200rpx;
  background-color: #f5f5f5;
}

.favorite-info {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
}

.favorite-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.favorite-price {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff8c00;
  margin-bottom: 12rpx;
}

.favorite-free {
  font-size: 32rpx;
  font-weight: 600;
  color: #4caf50;
  margin-bottom: 12rpx;
}

.favorite-meta {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 16rpx;
}

.favorite-actions {
  display: flex;
  gap: 16rpx;
  margin-top: auto;
}

/* 无数据提示样式 */
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.no-data-icon {
  margin-bottom: 20rpx;
}

.no-data-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 30rpx;
}

.no-data-btn {
  padding: 16rpx 40rpx;
  background: #ff8c00;
  color: white;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
}

/* 暗黑模式样式 */
.darkMode .container {
  background: #1c1c1e;
}

.darkMode .tabs {
  background: #2c2c2e;
  border-bottom: 1rpx solid #3a3a3c;
}

.darkMode .tab {
  color: #8e8e93;
}

.darkMode .tab.active {
  color: #ff8c00;
}

.darkMode .tab-content {
  background: #1c1c1e;
}

.darkMode .order-identity-selector {
  background: #2c2c2e;
}

.darkMode .identity-tab {
  color: #8e8e93;
  background: #3a3a3c;
}

.darkMode .identity-tab.active {
  color: #ff8c00;
  background: rgba(255, 140, 0, 0.2);
  border-color: #ff8c00;
}

.darkMode .goods-item,
.darkMode .order-item,
.darkMode .favorite-item {
  background: #2c2c2e;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.darkMode .goods-title,
.darkMode .order-title,
.darkMode .favorite-title {
  color: #f5f5f7;
}

.darkMode .goods-status-tag {
  background-color: #3a3a3c;
  color: #8e8e93;
}

.darkMode .goods-status-tag.on_shelf {
  color: #4caf50;
  background-color: rgba(76, 175, 80, 0.2);
}

.darkMode .goods-status-tag.off_shelf {
  color: #ff9800;
  background-color: rgba(255, 152, 0, 0.2);
}

.darkMode .goods-status-tag.sold_out {
  color: #f44336;
  background-color: rgba(244, 67, 54, 0.2);
}

.darkMode .goods-actions {
  background: #3a3a3c;
  border-top: 1rpx solid #48484a;
}

.darkMode .goods-stats {
  color: #8e8e93;
  background: #3a3a3c;
  border-top: 1rpx solid #48484a;
}

.darkMode .action-btn {
  background-color: #3a3a3c;
  color: #8e8e93;
}

.darkMode .action-btn.edit {
  background-color: rgba(33, 150, 243, 0.2);
  color: #2196f3;
}

.darkMode .action-btn.on-shelf {
  background-color: rgba(76, 175, 80, 0.2);
  color: #4caf50;
}

.darkMode .action-btn.off-shelf {
  background-color: rgba(255, 152, 0, 0.2);
  color: #ff9800;
}

.darkMode .action-btn.delete {
  background-color: rgba(244, 67, 54, 0.2);
  color: #f44336;
}

.darkMode .action-btn.verify {
  background-color: rgba(76, 175, 80, 0.2);
  color: #4caf50;
}

.darkMode .order-header {
  border-bottom: 1rpx solid #3a3a3c;
}

.darkMode .order-id {
  color: #8e8e93;
}

.darkMode .order-content {
  border-bottom: 1rpx solid #3a3a3c;
}

.darkMode .order-meta,
.darkMode .favorite-meta {
  color: #8e8e93;
}

.darkMode .no-data-text {
  color: #8e8e93;
}

/* 暗黑模式下的身份选择器样式 */
.darkMode .order-identity-selector .identity-tabs {
  background: #2c2c2e;
}

.darkMode .identity-tab {
  color: #8e8e93;
}

.darkMode .identity-tab.active {
  color: #ff8c00;
  background: #3a3a3c;
}

.darkMode .no-data-btn {
  background: #ff8c00;
  color: white;
}

/* 取消订单弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.cancel-modal {
  width: 600rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  margin: 0 30rpx;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  padding: 30rpx;
}

.reason-list {
  margin-bottom: 20rpx;
}

.reason-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  cursor: pointer;
}

.reason-item:last-child {
  border-bottom: none;
}

.reason-item.selected {
  color: #ff8c00;
}

.reason-text {
  font-size: 28rpx;
  flex: 1;
}

.reason-radio {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reason-item.selected .reason-radio {
  border-color: #ff8c00;
}

.radio-inner {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: transparent;
}

.radio-inner.selected {
  background: #ff8c00;
}

.custom-reason-item {
  border-bottom: 1rpx solid #f0f0f0;
}

.custom-reason {
  margin-top: 20rpx;
  padding: 0 20rpx;
}

.input-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.custom-input {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #f9f9f9;
  box-sizing: border-box;
  line-height: 1.4;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
}

.cancel-btn {
  color: #666;
  border-right: 1rpx solid #f0f0f0;
}

.confirm-btn {
  color: #ff8c00;
}

/* 暗黑模式下的弹窗样式 */
.darkMode .cancel-modal {
  background: #2c2c2e;
}

.darkMode .modal-header {
  border-bottom: 1rpx solid #3a3a3c;
}

.darkMode .modal-title {
  color: #f5f5f7;
}

.darkMode .modal-close {
  color: #8e8e93;
}

.darkMode .reason-item {
  border-bottom: 1rpx solid #3a3a3c;
}

.darkMode .reason-text {
  color: #f5f5f7;
}

.darkMode .input-label {
  color: #8e8e93;
}

.darkMode .custom-input {
  background: #3a3a3c;
  border: 1rpx solid #48484a;
  color: #f5f5f7;
}

.darkMode .modal-footer {
  border-top: 1rpx solid #3a3a3c;
}

.darkMode .cancel-btn {
  color: #8e8e93;
  border-right: 1rpx solid #3a3a3c;
}

.darkMode .reason-radio {
  border-color: #48484a;
}

.darkMode .reason-item.selected .reason-radio {
  border-color: #ff8c00;
}

.darkMode .custom-reason-item {
  border-bottom: 1rpx solid #3a3a3c;
}

/* ==================== 搜索、筛选和排序样式 ==================== */

/* 搜索框样式 */
.search-bar {
  padding: 20rpx 30rpx;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background: #f8f8f8;
  border-radius: 30rpx;
  padding: 16rpx 24rpx;
  position: relative;
}

.search-icon {
  margin-right: 16rpx;
  color: #999;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  background: transparent;
}

.search-input::placeholder {
  color: #999;
}

.clear-icon {
  margin-left: 16rpx;
  color: #999;
}

/* 分类导航样式 */
.category-scroll {
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
  white-space: nowrap;
}

.category-nav {
  display: flex;
  padding: 20rpx 30rpx;
}

.category-item {
  flex-shrink: 0;
  padding: 12rpx 24rpx;
  margin-right: 20rpx;
  background: #f8f8f8;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.category-item:last-child {
  margin-right: 30rpx;
}

.category-item.active {
  background: #ff8c00;
  color: white;
}

/* 筛选和排序样式 */
.filter-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
  overflow-x: auto;
  white-space: nowrap;
}

.filter-item {
  flex-shrink: 0;
  padding: 12rpx 20rpx;
  margin-right: 20rpx;
  background: #f8f8f8;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666;
  transition: all 0.3s ease;
}

.filter-item:last-child {
  margin-right: 0;
}

.filter-item.active {
  background: #ff8c00;
  color: white;
}

/* 暗黑模式适配 */
.darkMode .search-bar {
  background: #1c1c1e;
  border-bottom-color: #3a3a3c;
}

.darkMode .search-input-wrapper {
  background: #2c2c2e;
}

.darkMode .search-input {
  color: #ffffff;
}

.darkMode .search-input::placeholder {
  color: #8e8e93;
}

.darkMode .search-icon,
.darkMode .clear-icon {
  color: #8e8e93;
}

.darkMode .category-scroll {
  background: #1c1c1e;
  border-bottom-color: #3a3a3c;
}

.darkMode .category-item {
  background: #2c2c2e;
  color: #ffffff;
}

.darkMode .category-item.active {
  background: #ff8c00;
  color: white;
}

.darkMode .filter-bar {
  background: #1c1c1e;
  border-bottom-color: #3a3a3c;
}

.darkMode .filter-item {
  background: #2c2c2e;
  color: #ffffff;
}

.darkMode .filter-item.active {
  background: #ff8c00;
  color: white;
}
