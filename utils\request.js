

var apiUrl = wx.getStorageSync('apiUrl') // mao

// 常用接口列表，这些接口将使用缓存
const CACHEABLE_APIS = [
  // '/api/permission'
];

// 检查接口是否可缓存
const isCacheable = (url) => {
  return CACHEABLE_APIS.some(api => url.includes(api));
};

// 获取缓存键
const getCacheKey = (url, method, data) => {
  return `${url}_${method}_${JSON.stringify(data || {})}`;
};

// 检查缓存是否有效
const isCacheValid = (cache) => {
  return cache &&
    cache.timestamp &&
    (new Date().getTime() - cache.timestamp < CACHE_EXPIRY);
};

const request = (url, method, data, needToken) => {
  // 对于可缓存的GET请求，检查缓存
  if (method === 'GET' && isCacheable(url)) {
    const cacheKey = getCacheKey(url, method, data);

    // 如果有有效缓存，直接返回缓存的Promise或数据
    if (requestCache[cacheKey]) {
      // 如果请求正在进行中，返回现有Promise
      if (requestCache[cacheKey].promise && !requestCache[cacheKey].completed) {
        console.log(`请求${url}正在进行中，复用现有请求`);
        return requestCache[cacheKey].promise;
      }

      // 如果有有效的缓存数据，直接返回
      if (isCacheValid(requestCache[cacheKey])) {
        console.log(`使用缓存数据: ${url}`);
        return Promise.resolve(requestCache[cacheKey].data);
      }
    }

    // 创建新的Promise并缓存
    const promise = new Promise((resolve, reject) => {
      console.log(`发起新请求: ${url}`);
      baseRequest(url, method, data, needToken,
        // 成功回调
        (response) => {
          // 更新缓存
          requestCache[cacheKey] = {
            promise: promise,
            timestamp: new Date().getTime(),
            data: response,
            completed: true
          };
          resolve(response);
        },
        // 失败回调
        (error) => {
          // 标记请求完成但失败
          if (requestCache[cacheKey]) {
            requestCache[cacheKey].completed = true;
          }
          reject(error);
        }
      );
    });

    // 存储进行中的Promise
    requestCache[cacheKey] = {
      promise: promise,
      timestamp: new Date().getTime(),
      completed: false
    };

    return promise;
  }

  // 对于不可缓存的请求，正常处理
  return new Promise((resolve, reject) => {
    baseRequest(url, method, data, needToken, resolve, reject);
  });
}


const uploadPhoto = ({ url, method, filePath, formData }) => {

  let headerGet = {
    "Content-Type": "multipart/form-data",
    'Authorization': wx.getStorageSync('access_token')
  }

  // 对于非前置请求，添加小区ID到请求头
  if (!isPrerequisiteRequest(url)) {
    const selectedCommunity = wx.getStorageSync('selectedCommunity');
    if (selectedCommunity && selectedCommunity.id) {
      headerGet['CommunityId'] = selectedCommunity.id;
      console.log(`文件上传请求 ${url} 添加小区ID: ${selectedCommunity.id}`);
    } else {
      console.log(`文件上传请求 ${url} 未添加小区ID - 小区信息:`, selectedCommunity);
    }
  } else {
    console.log(`文件上传请求 ${url} 是前置请求，跳过添加小区ID`);
  }

  wx.showLoading({
    title: '上传中',
  });

  return new Promise((resolve, reject) => {

    wx.uploadFile({
      url: apiUrl + url,
      method,
      header: headerGet,
      filePath: filePath,
      name: 'file',
      formData: formData,
      success: res => {

        wx.hideLoading();
        var jj = JSON.parse(res.data);
        let code = jj.code
        switch (code) {
          case 0:
            resolve(jj.result);
            break;
          case 200:
            // other handlers
            break
          case 503 || 500:
            wx.hideLoading();
            wx.showModal({
              title: '错误提示',
              content: '上传图片失败',
              showCancel: false,

            })
            break
          default:
            reject(res)
        }
      },
      fail: function (error) {
        console.log('error', error)
        wx.hideLoading();
        wx.showModal({
          title: '错误提示',
          content: '上传图片失败',
          showCancel: false,

        })
        reject(error)
      }
    });
  })
}



// 添加全局变量来跟踪各种请求状态
var isLoginInProgress = false;
var loginPromise = null;
var isRefreshingToken = false;
var refreshTokenPromise = null;
var requestQueue = []; // 请求队列，用于存储等待登录/token刷新的请求
var isAppInitialized = false; // 标记应用是否已初始化完成

// 检查用户是否已登录
const checkUserLogin = () => {
  const userInfo = wx.getStorageSync('userInfo');
  const accessToken = wx.getStorageSync('access_token');
  return userInfo && accessToken;
};

// 检查token是否过期
const isTokenExpired = () => {
  const accessToken = wx.getStorageSync('access_token');
  if (!accessToken) return true;

  // 这里可以添加token过期时间检查逻辑
  // 目前简单检查token是否存在
  return false;
};

// 应用启动时立即检查登录状态
const checkInitialLoginStatus = () => {
  console.log('检查应用启动时的登录状态');

  // 如果已经有有效的登录信息，直接标记为已初始化
  if (checkUserLogin() && !isTokenExpired()) {
    console.log('应用启动时发现有效登录状态，直接标记为已初始化');
    isAppInitialized = true;
    return true;
  }

  console.log('应用启动时未发现有效登录状态');
  return false;
};

// 立即执行初始状态检查
checkInitialLoginStatus();

// 请求缓存对象，用于存储常用接口的请求结果
var requestCache = {
  // 格式: 'url_method': { promise: Promise, timestamp: Date, data: Object }
};

// 缓存有效期（毫秒）
const CACHE_EXPIRY = 10000; // 10秒，减少缓存时间

// 清除请求缓存
const clearRequestCache = () => {
  console.log('清除请求缓存');
  requestCache = {};
};

// 初始化应用登录状态
const initializeApp = () => {
  return new Promise((resolve, reject) => {
    console.log('开始初始化应用登录状态');

    // 检查是否已有有效的登录信息
    if (checkUserLogin() && !isTokenExpired()) {
      console.log('用户已登录且token有效，直接标记为已初始化');
      isAppInitialized = true;
      // 处理队列中可能存在的请求
      processRequestQueue();
      resolve(true);
      return;
    }

    // 需要登录
    console.log('需要进行登录');
    doLogin()
      .then((result) => {
        console.log('应用初始化登录成功');
        isAppInitialized = true;
        // 处理队列中的请求
        processRequestQueue();
        resolve(result);
      })
      .catch((error) => {
        console.error('应用初始化登录失败', error);
        isAppInitialized = true; // 即使失败也标记为已初始化，避免无限等待
        // 处理队列中的请求，全部失败
        processRequestQueue(error);
        reject(error);
      });
  });
};

// 检查是否为前置请求（不需要小区ID的请求）
const isPrerequisiteRequest = (url) => {
  // 登录相关请求
  if (url.includes('/auth/token') || url.includes('/oauth/login') || url.includes('/auth/refresh-token')) {
    return true;
  }

  // 小区选择相关请求
  if (url.includes('/community/page')) {
    return true;
  }

  // 用户信息相关请求（获取用户基本信息，不依赖小区）
  if (url.includes('/member/info') || url.includes('/member/logout')) {
    return true;
  }

  // 字典数据请求（全局数据，不依赖小区）
  if (url.includes('/dict/page')) {
    return true;
  }

  // 组织架构请求（全局数据，不依赖小区）
  // if (url.includes('/org/tree') || url.includes('/position/page')) {
  //   return true;
  // }

  return false;
};

// 检查是否需要等待登录
const shouldWaitForLogin = (url, needToken) => {
  // 如果是登录相关的请求，不需要等待
  if (url.includes('/auth/token') || url.includes('/oauth/login')) {
    return false;
  }

  // 如果不需要token的请求，不需要等待
  if (!needToken) {
    return false;
  }

  // 如果已经有有效的token，不需要等待
  if (checkUserLogin() && !isTokenExpired()) {
    return false;
  }

  // 如果应用还未初始化完成，且正在登录中，则需要等待
  return !isAppInitialized && isLoginInProgress;
};

// 清除用户登录信息
const clearUserInfo = () => {
  wx.removeStorageSync('userInfo');
  wx.removeStorageSync('access_token');
  wx.removeStorageSync('refresh_token');
  wx.removeStorageSync('openid');
  wx.removeStorageSync('session_key');
  clearRequestCache();
};

// 刷新token函数
const refreshToken = () => {
  // 如果已经在刷新token，返回现有的Promise
  if (isRefreshingToken && refreshTokenPromise) {
    console.log('Token刷新已在进行中，复用现有请求');
    return refreshTokenPromise;
  }

  const refreshTokenValue = wx.getStorageSync('refresh_token');
  if (!refreshTokenValue) {
    console.log('没有refresh_token，需要重新登录');
    return Promise.reject(new Error('没有refresh_token'));
  }

  // 标记开始刷新token
  isRefreshingToken = true;

  // 创建刷新token的Promise
  refreshTokenPromise = new Promise((resolve, reject) => {
    const refreshUrl = apiUrl + '/users-api/v1/auth/refresh-token?refreshToken=' + refreshTokenValue;

    wx.request({
      url: refreshUrl,
      method: 'POST',
      success(res) {
        console.log('刷新token响应', res);

        if (res.data.code === 0) {
          const tokenData = res.data.data;

          // 更新token信息
          wx.setStorageSync('access_token', tokenData.accessToken.access_token);
          wx.setStorageSync('refresh_token', tokenData.accessToken.refresh_token);

          console.log('Token刷新成功');
          resolve(tokenData);
        } else {
          console.log('Token刷新失败', res.data);
          reject(new Error(res.data.message || 'Token刷新失败'));
        }
      },
      fail(error) {
        console.log('Token刷新请求失败', error);
        reject(error);
      },
      complete() {
        // 无论成功失败，标记刷新完成
        isRefreshingToken = false;
        refreshTokenPromise = null;
      }
    });
  });

  return refreshTokenPromise;
};

// 处理请求队列
const processRequestQueue = (error = null) => {
  console.log(`开始处理请求队列，队列长度: ${requestQueue.length}`);

  if (requestQueue.length === 0) {
    console.log('请求队列为空，无需处理');
    return;
  }

  const queue = [...requestQueue];
  requestQueue.length = 0; // 清空队列

  console.log(`处理队列中的 ${queue.length} 个请求`);

  queue.forEach(({ resolve, reject, url, method, data, needToken }, index) => {
    console.log(`处理队列请求 ${index + 1}/${queue.length}: ${url}`);

    if (error) {
      console.log(`队列请求 ${url} 因错误被拒绝:`, error);
      reject(error);
    } else {
      // 重新发起请求
      baseRequest(url, method, data, needToken, resolve, reject);
    }
  });
};

// 登录函数，返回Promise
const doLogin = function () {
  // 如果已经在登录中，直接返回现有的Promise
  if (isLoginInProgress && loginPromise) {
    console.log('登录已在进行中，复用现有请求');
    return loginPromise;
  }

  // 标记登录开始
  isLoginInProgress = true;

  // 创建新的登录Promise
  loginPromise = new Promise((loginResolve, loginReject) => {
    wx.login({
      success: loginRes => {
        wx.setStorageSync('code', loginRes.code);
        let code = loginRes.code;

        if (!apiUrl) {
          apiUrl = wx.getStorageSync('apiUrl')
        }
        let tokenUrl = apiUrl + '/users-api/v1/auth/token?code=' + code;


        wx.request({
          url: tokenUrl,
          method: 'POST',
          data: {

          },
          success(res) {
            console.log('wx.login', res);

            if (res.data.code == 0) {
              var red = res.data.data;

              wx.setStorageSync('access_token', red.accessToken.access_token);
              wx.setStorageSync('refresh_token', red.accessToken.refresh_token);
              wx.setStorageSync('openid', red.memberDetail.openid);
              wx.setStorageSync('session_key', red.session_key);
              wx.setStorageSync('userInfo', red.memberDetail);

              // 登录成功后清除缓存，确保重新获取数据
              clearRequestCache();
              // 登录成功，解决Promise
              loginResolve(res.data);
            } else {
              // 登录失败，拒绝Promise
              loginReject(res.data);
            }
          },
          fail(error) {
            console.log('登录请求失败', error);
            loginReject(error);
          },
          complete() {
            // 无论成功失败，标记登录完成
            isLoginInProgress = false;
          }
        });
      },
      fail(error) {
        console.log('wx.login失败', error);
        loginReject(error);
        isLoginInProgress = false;
      }
    });
  });

  return loginPromise;
};

var reqObj = function () { }
const baseRequest = function (url, method, data, needToken, resolve, reject) {

  if (apiUrl == null || apiUrl == '' || apiUrl == undefined)
    return

  // 检查是否需要等待登录完成
  if (shouldWaitForLogin(url, needToken)) {
    console.log(`请求${url}需要等待登录完成，加入队列，当前队列长度: ${requestQueue.length}`);
    requestQueue.push({ resolve, reject, url, method, data, needToken });
    console.log(`请求已加入队列，新队列长度: ${requestQueue.length}`);
    return;
  }

  let _url = apiUrl + url
  let dict

  if (needToken) {
    let token = wx.getStorageSync('access_token')
    dict = { 'content-type': 'application/json', 'Authorization': token }
  } else {
    dict = { 'content-type': 'application/json' }
  }

  // 对于非前置请求，添加小区ID到请求头
  if (!isPrerequisiteRequest(url)) {
    const selectedCommunity = wx.getStorageSync('selectedCommunity');
    if (selectedCommunity && selectedCommunity.id) {
      dict['CommunityId'] = selectedCommunity.id;
      console.log(`请求 ${url} 添加小区ID: ${selectedCommunity.id}`);
    } else {
      console.log(`请求 ${url} 未添加小区ID - 小区信息:`, selectedCommunity);
    }
  } else {
    console.log(`请求 ${url} 是前置请求，跳过添加小区ID`);
  }

  // 如果是登录请求，避免重复调用
  if (url === '/oauth/loginV1' && isLoginInProgress) {
    console.log('避免重复登录请求');
    loginPromise.then(resolve).catch(reject);
    return;
  }

  reqObj.task = wx.request({
    url: _url,
    method: method,
    data: data,
    header: dict,
    success(sres) {
      if (sres.data.code == 401) {
        console.log(url + ' 401 未授权', sres);

        // 检查是否有用户信息，决定是刷新token还是重新登录
        const userInfo = wx.getStorageSync('userInfo');
        const refreshTokenValue = wx.getStorageSync('refresh_token');

        if (userInfo && refreshTokenValue) {
          // 有用户信息和refresh_token，尝试刷新token
          console.log('尝试刷新token');

          // 如果正在刷新token，将请求加入队列
          if (isRefreshingToken) {
            console.log(`Token正在刷新中，将请求 ${url} 加入队列，当前队列长度: ${requestQueue.length}`);
            requestQueue.push({ resolve, reject, url, method, data, needToken });
            console.log(`请求已加入队列，新队列长度: ${requestQueue.length}`);
            return;
          }

          // 开始刷新token
          refreshToken()
            .then(() => {
              console.log('Token刷新成功，重新发起原始请求和队列请求');
              // 刷新成功后，重新发起原始请求
              baseRequest(url, method, data, needToken, resolve, reject);
              // 处理队列中的其他请求
              processRequestQueue();
            })
            .catch(error => {
              console.log('Token刷新失败，清除用户信息并重新登录', error);
              // 刷新失败，清除用户信息
              clearUserInfo();
              // 处理队列中的请求，全部失败
              processRequestQueue(error);

              // 尝试重新登录
              doLogin()
                .then(() => {
                  // 登录成功后，重新发起原始请求
                  baseRequest(url, method, data, needToken, resolve, reject);
                })
                .catch(loginError => {
                  console.log('重新登录失败', loginError);
                  wx.showToast({
                    title: '登录失败，请重试',
                    icon: 'none'
                  });
                  reject(loginError);
                });
            });
        } else {
          // 没有用户信息或refresh_token，直接重新登录
          // console.log('没有用户信息，直接重新登录');
          doLogin()
            .then(() => {
              // 登录成功后，重新发起原始请求
              baseRequest(url, method, data, needToken, resolve, reject);
            })
            .catch(error => {
              console.log('登录失败', error);
              wx.showToast({
                title: '登录失败，请重试',
                icon: 'none'
              });
              reject(error);
            });
        }
      } else {

        wx.hideLoading();
        if (sres.statusCode != 200) {
          console.log(sres.data);
          wx.showToast({
            title: sres.data.errorMessage ? sres.data.errorMessage : '服务器错误',
            icon: 'none'
          });
          reject(sres);
        } else {

          if (sres.data.code != null && sres.data.code != undefined) {

            if (sres.data.code == 0 && sres.data.data != false) {

              resolve(sres.data.data);
            } else {
              console.log(sres.data);

              wx.showToast({
                title: sres.data.errorMessage,
                icon: 'none'
              });
              reject(sres.data);
            }
          } else {

            resolve(sres.data);
          }



        }
      }
    },
    fail(error) {
      wx.hideLoading();
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
      reject(error);
    },
    complete(res) {
      // 加载完成
    }
  });
}


/**
 * 小程序的promise没有finally方法，自己扩展下
 */
Promise.prototype.finally = function (callback) {
  var Promise = this.constructor;
  return this.then(
    function (value) {
      Promise.resolve(callback()).then(
        function () {
          return value;
        }
      );
    },
    function (reason) {
      Promise.resolve(callback()).then(
        function () {
          throw reason;
        }
      );
    }
  );
}


//所有接口定义在这里
module.exports = {
  reqObj,
  request,
  uploadPhoto: uploadPhoto,
  doLogin,
  clearRequestCache,
  checkUserLogin,
  clearUserInfo,
  refreshToken,
  initializeApp,
  isTokenExpired
}