// pages/goods/verification/scan.js
const dateUtil = require('@/utils/dateUtil.js')
const goodsApi = require('@/api/goods.js')
const util = require('@/utils/util.js')
const commApi = require('@/api/commApi.js')
Page({
  data: {
    darkMode: false,
    goodsId: null,
    scanResult: null,
    verificationSuccess: false,
    verificationFailed: false,
    errorMessage: '',
    orderInfo: null,
    showOrderConfirm: false, // 是否显示订单确认界面


    isLoading: false,
    loadingText: '',

    // 邀请码绑定弹窗相关数据
    showInviteModal: false,
    inviteData: null, // 邀请码数据（住户信息 + 房产列表）
    isBinding: false, // 是否正在绑定
    currentInviteId: '', // 当前邀请码ID
    currentInviteToken: '', // 当前邀请码Token

  },

  onLoad: function (options) {
    if (options.goodsId) {
      this.setData({
        goodsId: options.goodsId
      })
    }

    // 自动启动扫码
    this.startScan()
  },

  // 检测订单是否超时
  checkOrderExpired: function (expireTime) {
    if (!expireTime) {
      return false; // 没有过期时间，认为未超时
    }

    try {
      const now = new Date();
      const expireDate = new Date(expireTime);

      console.log('扫码核销超时检测:', {
        now: now.toISOString(),
        expireTime: expireTime,
        expireDate: expireDate.toISOString(),
        isExpired: now > expireDate
      });

      return now > expireDate;
    } catch (error) {
      console.error('解析过期时间失败:', error);
      return false;
    }
  },

  // 启动扫码
  startScan: function () {
    wx.scanCode({
      onlyFromCamera: true,
      scanType: ['qrCode'],
      success: (res) => {
        console.log('扫码结果:', res)
        this.setData({
          scanResult: res.result
        })

        // 验证二维码
        // this.verifyQRCode(res.result)

        this.handleScanResult(res.result);

      },
      fail: (err) => {
        console.log('扫码失败:', err)
        if (err.errMsg !== 'scanCode:fail cancel') {
          wx.showToast({
            title: '扫码失败',
            icon: 'none'
          })
        }

        // 返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
    })
  },


  // 处理扫描结果
  handleScanResult: function (qrCodeContent) {
    console.log('处理扫码内容:', qrCodeContent);

    // 检查二维码格式
    if (qrCodeContent.startsWith('roomCode:')) {
      // 邀请二维码格式：roomCode:id:token
      this.handleInviteCode(qrCodeContent);
    } else if (qrCodeContent.startsWith('order:')) {
      // 订单二维码格式：order:id
      this.verifyQRCode(qrCodeContent);
    } else if (qrCodeContent.startsWith('visitor:')) {
      // 访客二维码格式：visitor:id
      this.handleVisitorCode(qrCodeContent);
    } else {
      wx.showToast({
        title: '不支持的二维码格式',
        icon: 'none'
      });
    }
  },

  // 处理访客二维码
  handleVisitorCode: function (qrCodeContent) {
    console.log('处理访客二维码:', qrCodeContent);

    // 解析访客码：visitor:id
    const parts = qrCodeContent.split(':');
    if (parts.length !== 2) {
      wx.showToast({
        title: '访客码格式错误',
        icon: 'none'
      });
      return;
    }

    const visitorId = parts[1];

    if (!visitorId) {
      wx.showToast({
        title: '访客ID无效',
        icon: 'none'
      });
      return;
    }

    // 跳转到访客凭证页面，添加扫码标识
    wx.navigateTo({
      url: `/servicePackage/pages/visitor/credential/index?id=${visitorId}&fromScan=true`
    });
  },

  // 处理邀请二维码
  handleInviteCode: function (qrCodeContent) {
    console.log('处理邀请二维码:', qrCodeContent);

    // 解析邀请码：roomCode:id:token
    const parts = qrCodeContent.split(':');
    if (parts.length !== 3) {
      wx.showToast({
        title: '邀请码格式错误',
        icon: 'none'
      });
      return;
    }

    const inviteId = parts[1];
    const inviteToken = parts[2];

    if (!inviteId || !inviteToken) {
      wx.showToast({
        title: '邀请码参数无效',
        icon: 'none'
      });
      return;
    }

    // 保存邀请码信息
    this.setData({
      currentInviteId: inviteId,
      currentInviteToken: inviteToken,
      isLoading: true,
      loadingText: '验证邀请码...'
    });

    // 调用API验证邀请码
    this.verifyInviteCode(inviteId, inviteToken);
  },


  // 验证邀请码
  verifyInviteCode: function (inviteId, inviteToken) {
    console.log('验证邀请码:', { inviteId, inviteToken });

    // 构造API参数
    const params = {
      id: inviteId,
      token: inviteToken
    };

    commApi.getQrCodeWithOne(params)
      .then(res => {
        console.log('邀请码验证结果:', res);

        if ( res) {
          // 处理邀请码数据
          this.processInviteData(res);
        } else {
          wx.showToast({
            title: res.errorMessage || '邀请码无效或已过期',
            icon: 'none'
          });
          this.setData({
            isLoading: false
          });
        }
      })
      .catch(err => {
        console.error('验证邀请码失败:', err);
     
        this.setData({
          isLoading: false
        });
      });
  },


  // 处理邀请码数据
  processInviteData: function (data) {
    console.log('处理邀请码数据:', data);

    // 获取住户信息的首字母
    const nameInitial = data.residentName ? data.residentName.charAt(0) : '?';


    // 处理房产列表
    const roomList = data.roomList || [];
    const processedRoomList = roomList.map(room => ({
      ...room,
      fullAddress: this.formatHouseAddress(room),
      residentTypeName: this.formatResidentType(room),
      isBind: room.isBind || false
    }));

    // 设置邀请数据
    const inviteData = {
      ...data,
      nameInitial,
      roomList: processedRoomList
    };

    this.setData({
      inviteData,
      showInviteModal: true,
      isLoading: false
    });
  },

  //格式化住户类型
  formatResidentType: function (room) {

    var residentTypeDict = util.getDictByNameEn('resident_type');

    if (residentTypeDict && residentTypeDict.length > 0 && residentTypeDict[0].children) {
      {
        var residentTypeOptions = residentTypeDict[0].children

        for (let index = 0; index < residentTypeOptions.length; index++) {
          const dict = residentTypeOptions[index];

          if (dict.nameEn == room.residentType) {
            return dict.nameCn

          }
        }
      }
    }
  },


  // 格式化房屋地址
  formatHouseAddress: function (house) {
    const parts = [];
    if (house.buildingNumber) parts.push(house.buildingNumber);
    if (house.unitNumber) parts.push(house.unitNumber);
    if (house.roomNumber) parts.push(house.roomNumber);
    return parts.join(' ') || house.address || `房间${house.roomId}`;
  },


  // 隐藏邀请弹窗
  hideInviteModal: function() {
    this.setData({
      showInviteModal: false,
      inviteData: null,
      currentInviteId: '',
      currentInviteToken: '',
      isBinding: false
    });
  },

  // 确认绑定
  confirmBinding: function() {
    if (!this.data.currentInviteId || !this.data.currentInviteToken) {
      wx.showToast({
        title: '邀请码信息错误',
        icon: 'none'
      });
      return;
    }

    this.setData({
      isBinding: true
    });

    // 构造绑定API参数
    const params = {
      id: this.data.currentInviteId,
      token: this.data.currentInviteToken
    };

    // 调用绑定API
    commApi.useQrCodeWithOne(params)
      .then(res => {
        console.log('绑定结果:', res);

    
          wx.showToast({
            title: '绑定成功',
            icon: 'success'
          });

          // 关闭弹窗
          this.hideInviteModal();

          // 延迟返回首页
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/index/index'
            });
          }, 1500);
      
      })
      .catch(err => {
        console.error('绑定失败:', err);
    
        this.setData({
          isBinding: false
        });
      });
  },

  // 防止滑动穿透
  preventTouchMove: function() {
    return false;
  },


  // 验证二维码
  verifyQRCode: function (qrCodeContent) {
    wx.showLoading({
      title: '验证中...'
    });

    console.log('扫码内容:', qrCodeContent);

    // 检查二维码格式：order: + 订单id
    if (!qrCodeContent.startsWith('order:')) {
      this.showVerificationError('无效的订单二维码');
      return;
    }

    // 提取订单ID
    const orderId = qrCodeContent.split(':')[1];
    const orderToken = qrCodeContent.split(':')[2];
    if (!orderId||!orderToken) {
      this.showVerificationError('订单ID无效');
      return;
    }

    // 获取订单详情
    goodsApi.getMyGoodsOrderDetail(orderId).then(res => {
      if ( res) {
        const orderData = res;

        // 检查订单状态
        if (orderData.status === 'complete') {
          this.showVerificationError('订单已完成，无需重复核销');
          return;
        }

        if (orderData.status === 'cancel') {
          this.showVerificationError('订单已取消，无法核销');
          return;
        }

        // 检查订单是否超时
        if (this.checkOrderExpired(orderData.expireTime)) {
          this.showVerificationError('订单已超时，无法完成核销');
          return;
        }

        // 解析商品信息
        let goodsInfo = {};
        if (orderData.stuffSnapshot) {
          try {
            goodsInfo = JSON.parse(orderData.stuffSnapshot);
          } catch (e) {
            console.error('解析商品快照失败:', e);
          }
        }

        // 显示订单确认信息
        this.setData({
          orderInfo: {
            id: orderData.id,
            orderToken:orderToken,
            orderNo: orderData.orderNo || '',
            buyerName: '买家', // 可以从订单数据中获取
            stuffDescribe: goodsInfo.stuffDescribe || '',
            quantity: orderData.quantity || 1,
            totalAmount: orderData.totalAmount || 0,
            unitAmount: orderData.unitAmount || 0,
            createTime: orderData.createTime ? dateUtil.formatTime(new Date(orderData.createTime)) : '',
            status: orderData.status,
            address: goodsInfo.address || ''
          },
          showOrderConfirm: true
        });

        wx.hideLoading();
      } else {
        this.showVerificationError(res.message || '订单不存在');
      }
    }).catch(err => {
      console.error('获取订单详情失败:', err);
      
      this.showVerificationError('获取订单信息失败，请重试');
    });
  },

  // 显示验证错误
  showVerificationError: function (message) {
    this.setData({
      verificationFailed: true,
      errorMessage: message
    });

    wx.hideLoading();

    
  },

  // 确认完成订单
  confirmCompleteOrder: function () {
    const orderInfo = this.data.orderInfo;
    if (!orderInfo || !orderInfo.id) {
      wx.showToast({
        title: '订单信息错误',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '处理中...'
    });

    // 调用完成订单API
    goodsApi.completeMyGoodsOrder({
      id: orderInfo.id,
      token: orderInfo.orderToken,
      note: '卖家扫码完成订单'
    }).then(res => {
   
        // 更新UI显示成功
        this.setData({
          verificationSuccess: true,
          showOrderConfirm: false,
          'orderInfo.verifyTime': dateUtil.formatTime(new Date())
        });

        wx.hideLoading();

        wx.showToast({
          title: '核销成功',
          icon: 'success'
        });
   
    }).catch(err => {
      console.error('完成订单失败:', err);
      wx.hideLoading();
   
    });
  },

  // 取消确认
  cancelConfirm: function () {
    this.setData({
      showOrderConfirm: false,
      orderInfo: null
    });

    wx.navigateBack()
  },

  // 重新扫码
  rescan: function () {
    this.setData({
      scanResult: null,
      verificationSuccess: false,
      verificationFailed: false,
      errorMessage: '',
      orderInfo: null,
      showOrderConfirm: false
    })

    this.startScan()
  },

  // 返回列表
  backToList: function () {
    wx.navigateBack()
  },

  // 查看订单详情
  viewOrderDetail: function () {
    wx.navigateTo({
      url: `/profilePackage/pages/goods/order/order?id=${this.data.orderInfo.id}&searchType=seller`
    })
  }
})
