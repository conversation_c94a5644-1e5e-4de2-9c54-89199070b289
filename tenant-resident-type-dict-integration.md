# 租客列表页面residentType字典集成

## 概述
将租客列表页面中的`residentType`字段显示改为使用`resident_type`字典，实现动态显示租客类型的中文名称。

## 修改内容

### 1. 问题分析

#### 原代码问题（硬编码）
```javascript
// 原代码 - 硬编码方式显示租客类型
let relationText = '租客';
let relationClass = 'relation-tenant';
if (member.residentType === 'tenant_family') {
  relationText = '租客家属';
  relationClass = 'relation-tenant-family';
}
```

**问题**：
- 硬编码了具体的状态值（`tenant_family`）
- 硬编码了显示文本（`租客`、`租客家属`）
- 不支持字典中新增的租客类型
- 维护困难，需要修改代码才能支持新类型

### 2. 修复方案

#### 字典驱动的显示逻辑
```javascript
// 修复后 - 使用字典驱动的方式
// 根据residentType使用字典显示关系文本
let relationText = '租客'; // 默认值
let relationClass = 'relation-tenant'; // 默认样式

// 从字典中查找对应的显示文本
const residentTypeOption = this.data.residentTypes.find(type => 
  type.nameEn === member.residentType
);

if (residentTypeOption) {
  relationText = residentTypeOption.nameCn;
  // 根据类型设置样式类
  relationClass = member.residentType === 'tenant_family' ? 'relation-tenant-family' : 'relation-tenant';
}
```

### 3. 页面生命周期优化

#### 添加onShow方法
```javascript
onShow: function () {
  // 页面显示时刷新租客列表，确保数据是最新的
  if (this.data.residentTypes.length > 0) {
    this.loadFamilyMembers(true)
  }
}
```

**作用**：
- 确保页面显示时数据是最新的
- 避免字典数据未加载完成就处理列表数据的问题
- 提供更好的用户体验

## 技术实现

### 1. 字典数据加载
```javascript
// 加载住户身份选项（只包含租客和租客家属）
loadResidentTypes: function () {
  try {
    const residentTypeDict = util.getDictByNameEn('resident_type');
    if (residentTypeDict && residentTypeDict.length > 0 && residentTypeDict[0].children) {
      // 过滤出租客和租客家属选项
      const residentOptions = residentTypeDict[0].children.filter(item =>
        item.nameEn === 'tenant' || item.nameEn === 'tenant_family'
      );
      this.setData({
        residentTypes: residentOptions
      });
      console.log('租客身份选项:', residentOptions);
    } else {
      console.warn('住户身份字典数据为空');
      this.setData({
        residentTypes: []
      });
    }
  } catch (error) {
    console.error('加载住户身份选项失败:', error);
    this.setData({
      residentTypes: []
    });
  }
}
```

### 2. 字典查找逻辑
```javascript
// 从字典中查找对应的显示文本
const residentTypeOption = this.data.residentTypes.find(type => 
  type.nameEn === member.residentType
);

if (residentTypeOption) {
  relationText = residentTypeOption.nameCn;
}
```

### 3. 数据处理流程
```
页面加载 → 加载字典数据 → 加载房屋列表 → 加载租客列表 → 字典匹配显示
```

## 数据结构

### 字典数据格式
```javascript
// resident_type字典结构
residentTypes: [
  { nameEn: 'tenant', nameCn: '租客' },
  { nameEn: 'tenant_family', nameCn: '租客家属' }
]
```

### API返回数据格式
```javascript
// 租客列表API返回的数据
{
  id: 'xxx',
  residentName: '张三',
  residentType: 'tenant', // 或 'tenant_family'
  // ... 其他字段
}
```

### 处理后的显示数据
```javascript
// 处理后用于显示的数据
{
  id: 'xxx',
  name: '张三',
  residentType: 'tenant',
  relationText: '租客', // 从字典获取的中文显示
  relationClass: 'relation-tenant', // 对应的样式类
  // ... 其他字段
}
```

## 优势对比

### 修复前（硬编码方式）
❌ **缺点**：
- 硬编码状态值和显示文本
- 新增租客类型需要修改代码
- 维护成本高
- 不支持动态配置

### 修复后（字典驱动）
✅ **优点**：
- 完全由字典数据驱动
- 支持字典中任意租客类型
- 无需修改代码即可支持新类型
- 维护成本低
- 支持动态配置

## 兼容性处理

### 1. 字典数据缺失
```javascript
// 如果字典数据为空，使用默认值
let relationText = '租客'; // 默认值
let relationClass = 'relation-tenant'; // 默认样式
```

### 2. 字典中找不到匹配项
```javascript
// 如果在字典中找不到对应项，使用默认值
if (residentTypeOption) {
  relationText = residentTypeOption.nameCn;
} else {
  // 保持默认值
  relationText = '租客';
}
```

### 3. 样式类处理
```javascript
// 样式类仍然需要根据具体类型设置，因为CSS样式是固定的
relationClass = member.residentType === 'tenant_family' ? 'relation-tenant-family' : 'relation-tenant';
```

## 测试验证

### 测试场景
1. **正常情况**：字典数据正常，租客类型为`tenant`或`tenant_family`
2. **字典缺失**：字典数据加载失败或为空
3. **未知类型**：租客类型不在字典中
4. **页面刷新**：页面显示时数据正确更新

### 验证要点
- 租客类型显示正确的中文名称
- 字典数据加载失败时有合理的兜底显示
- 页面刷新时数据正确更新
- 样式类正确应用

## 扩展性

### 支持新增租客类型
当字典中新增租客类型时（如`tenant_relative`），无需修改代码：

1. **字典更新**：在`resident_type`字典中添加新项
   ```javascript
   { nameEn: 'tenant_relative', nameCn: '租客亲属' }
   ```

2. **自动支持**：页面会自动显示新的租客类型
3. **样式适配**：如需特殊样式，只需在CSS中添加对应样式类

### 过滤逻辑
```javascript
// 只显示租客相关的类型
const residentOptions = residentTypeDict[0].children.filter(item =>
  item.nameEn === 'tenant' || 
  item.nameEn === 'tenant_family' ||
  item.nameEn === 'tenant_relative' // 新增类型自动支持
);
```

## 总结

这个修改实现了：

1. **数据驱动**：使用`resident_type`字典动态显示租客类型
2. **无硬编码**：移除了所有硬编码的状态值和显示文本
3. **可扩展性**：支持字典中新增任意租客类型
4. **健壮性**：完善的错误处理和兜底逻辑
5. **用户体验**：显示中文名称而非英文代码

通过这个改进，租客列表页面能够更灵活地显示不同类型的租客，同时保持代码的可维护性和扩展性。
