<!--我的好物页面-->
<view class="container {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}">
  <!-- 标签页切换 -->
  <view class="tabs">
    <view class="tab {{activeTab === 'published' ? 'active' : ''}}" bindtap="switchTab" data-tab="published">我的发布</view>
    <view class="tab {{activeTab === 'orders' ? 'active' : ''}}" bindtap="switchTab" data-tab="orders">我的订单</view>
    <view class="tab {{activeTab === 'favorites' ? 'active' : ''}}" bindtap="switchTab" data-tab="favorites">我的收藏</view>
  </view>

  <!-- 我的发布 -->
  <view class="tab-content" wx:if="{{activeTab === 'published'}}">
    <view class="goods-list">
      <view class="goods-item" wx:for="{{publishedGoods}}" wx:key="id">
        <!-- 右上角状态标签 -->
        <view class="goods-status-tag {{item.statusClass}}">{{item.statusName}}</view>

        <!-- 商品内容 -->
        <view class="goods-content">
          <image class="goods-image" src="{{item.images[0]}}" mode="aspectFill" bindtap="navigateToDetail" data-id="{{item.id}}"></image>
          <view class="goods-info">
            <view class="goods-title" bindtap="navigateToDetail" data-id="{{item.id}}">{{item.stuffDescribe}}</view>
            <view class="goods-tag-row">
              <view class="goods-type-tag">{{item.typeName}}</view>
            </view>
            <view class="goods-price" wx:if="{{!item.isFreeType}}">¥{{item.amount}}</view>
            <view class="goods-free" wx:else>{{item.typeName}}赠送</view>
            <view class="goods-meta">
              <view class="goods-location">{{item.address}}</view>
              <view class="goods-time">{{item.createTime}}</view>
            </view>
          </view>
        </view>

        <!-- 统计数据行 -->
        <view class="goods-stats">
          <view class="goods-stat">库存: {{item.stock}}</view>
          <view class="goods-stat">已售: {{item.soldStock}}</view>
          <view class="goods-stat">浏览: {{item.views}}</view>
          <view class="goods-stat">点赞: {{item.likeCount || 0}}</view>
          <view class="goods-stat">收藏: {{item.collectCount || 0}}</view>
        </view>

        <!-- 操作按钮 -->
        <view class="goods-actions">
          <view class="action-btn edit" bindtap="editGoods" data-id="{{item.id}}">编辑</view>
          <view class="action-btn {{item.status === 'on_shelf' ? 'off-shelf' : 'on-shelf'}}"
                bindtap="toggleShelfStatus"
                data-id="{{item.id}}"
                data-status="{{item.status}}">
            {{item.status === 'on_shelf' ? '下架' : '上架'}}
          </view>
          <view class="action-btn delete" bindtap="deleteGoods" data-id="{{item.id}}">删除</view>
          <view class="action-btn verify"
                bindtap="navigateToPendingVerifications"
                data-id="{{item.id}}"
                wx:if="{{item.pendingOrders > 0}}">
            待核销({{item.pendingOrders}})
          </view>
        </view>
      </view>
    </view>

    <view class="no-data" wx:if="{{publishedGoods.length === 0}}">
      <icon class="no-data-icon" type="info" size="40" color="#ccc"></icon>
      <view class="no-data-text">暂无发布的商品</view>
      <view class="no-data-btn" bindtap="navigateToPublish">去发布</view>
    </view>
  </view>

  <!-- 我的订单 -->
  <view class="tab-content" wx:if="{{activeTab === 'orders'}}">
    <!-- 订单身份选择器 -->
    <view class="order-identity-selector">
      <view class="identity-tabs">
        <view
          class="identity-tab {{currentOrderIdentity === item.nameEn ? 'active' : ''}}"
          wx:for="{{orderIdentityOptions}}"
          wx:key="nameEn"
          bindtap="switchOrderIdentity"
          data-identity="{{item.nameEn}}">
          {{item.nameCn}}
        </view>
      </view>
    </view>

    <view class="order-list">
      <view class="order-item" wx:for="{{orders}}" wx:key="id">
        <view class="order-header">
          <view class="order-id">订单号: {{item.orderNo}}</view>
          <view class="order-status {{item.status}}">
            {{item.statusName}}
          </view>
        </view>
        <view class="order-content" bindtap="navigateToOrder" data-id="{{item.id}}">
          <image class="order-image" src="{{item.medias[0]}}" mode="aspectFill"></image>
          <view class="order-info">
            <view class="order-title">{{item.stuffDescribe}}</view>
            <view class="order-tag-row">
              <view class="order-type-tag">{{item.typeName}}</view>
            </view>
            <view class="order-price" wx:if="{{!item.isFreeType}}">¥{{item.totalAmount}}</view>
            <view class="order-free" wx:else>{{item.typeName}} × {{item.quantity}}</view>
            <view class="order-meta">
            <view style="display: flex;">
              <view class="order-seller">卖家: {{item.sellerName}}</view>
              <view class="order-time">{{item.createTime}}</view>
            </view>
              
              <view class="order-address" wx:if="{{item.address}}">地址: {{item.address}}</view>
            </view>
          </view>
        </view>
        <view class="order-actions">
          <view class="action-btn contact" bindtap="contactSeller" data-id="{{item.sellerId}}" data-name="{{item.sellerName}}">联系卖家</view>
          <view class="action-btn cancel"
                bindtap="cancelOrder"
                data-id="{{item.id}}"
                wx:if="{{item.status === 'wait_complete'}}">
            取消订单
          </view>

          <view class="action-btn cancel"
                bindtap="deleteOrder"
                data-id="{{item.id}}"
                wx:if="{{item.status === 'cancel'}}">
            删除订单
          </view>
        </view>
      </view>
    </view>

    <view class="no-data" wx:if="{{orders.length === 0}}">
      <icon class="no-data-icon" type="info" size="40" color="#ccc"></icon>
      <view class="no-data-text">您还没有相关的订单</view>
      <view class="no-data-btn" bindtap="{{currentOrderIdentity === 'seller' ? 'navigateToPublish' : 'navigateToGoods'}}">
        {{currentOrderIdentity === 'seller' ? '去发布' : '去购物'}}
      </view>
    </view>
  </view>

  <!-- 我的收藏 -->
  <view class="tab-content" wx:if="{{activeTab === 'favorites'}}">
    <view class="favorite-list">
      <view class="favorite-item" wx:for="{{favorites}}" wx:key="id">
        <image class="favorite-image" src="{{item.images[0]}}" mode="aspectFill" bindtap="navigateToDetail" data-id="{{item.id}}"></image>
        <view class="favorite-info">
          <view class="favorite-title" bindtap="navigateToDetail" data-id="{{item.goodsId}}">{{item.stuffDescribe}}</view>
          <view class="favorite-tag-row">
            <view class="favorite-type-tag">{{item.typeName}}</view>
          </view>
          <view class="favorite-price" wx:if="{{!item.isFreeType}}">¥{{item.amount}}</view>
          <view class="favorite-free" wx:else>{{item.typeName}}赠送</view>
          <view class="favorite-meta">
            <view class="favorite-seller">卖家: {{item.memberName}}</view>
            <view class="favorite-time">{{item.favoriteTime}}</view>
          </view>
          <view class="favorite-actions">
            <view class="action-btn contact" bindtap="contactSeller" data-id="{{item.goodsId}}" data-name="{{item.sellerName}}">联系卖家</view>
            <view class="action-btn cancel" bindtap="cancelFavorite" data-id="{{item.id}}">取消收藏</view>
          </view>
        </view>
      </view>
    </view>

    <view class="no-data" wx:if="{{favorites.length === 0}}">
      <icon class="no-data-icon" type="info" size="40" color="#ccc"></icon>
      <view class="no-data-text">暂无收藏的商品</view>
      <view class="no-data-btn" bindtap="navigateToGoods">去逛逛</view>
    </view>
  </view>
</view>

<!-- 取消订单原因选择弹窗 -->
<view class="modal-overlay" wx:if="{{showCancelModal}}" bindtap="hideCancelModal">
  <view class="cancel-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <view class="modal-title">选择取消原因</view>
      <view class="modal-close" bindtap="hideCancelModal">×</view>
    </view>

    <view class="modal-content">
      <view class="reason-list">
        <!-- 前三个预设原因选项 -->
        <view
          class="reason-item {{selectedCancelReason === item.value ? 'selected' : ''}}"
          wx:for="{{cancelReasons}}"
          wx:for-index="index"
          wx:key="value"
          wx:if="{{index < 3}}"
          catchtap="selectCancelReason"
          data-reason="{{item.value}}">
          <view class="reason-text">{{item.text}}</view>
          <view class="reason-radio">
            <view class="radio-inner {{selectedCancelReason === item.value ? 'selected' : ''}}"></view>
          </view>
        </view>

        <!-- 其他原因选项 -->
        <view class="reason-item custom-reason-item {{selectedCancelReason === 'other' ? 'selected' : ''}}"
              catchtap="selectOtherReason">
          <view class="reason-text">其他原因</view>
          <view class="reason-radio">
            <view class="radio-inner {{selectedCancelReason === 'other' ? 'selected' : ''}}"></view>
          </view>
        </view>
      </view>

      <!-- 自定义原因输入框 -->
      <view class="custom-reason" wx:if="{{selectedCancelReason === 'other'}}">
        <view class="input-label">请详细说明取消原因：</view>
        <textarea
          class="custom-input"
          placeholder="请输入取消原因"
          value="{{customCancelReason}}"
          bindinput="inputCustomReason"
          maxlength="100"
          auto-focus="{{selectedCancelReason === 'other'}}"
          show-confirm-bar="{{false}}">
        </textarea>
      </view>
    </view>

    <view class="modal-footer">
      <view class="modal-btn cancel-btn" bindtap="hideCancelModal">取消</view>
      <view class="modal-btn confirm-btn" bindtap="confirmCancelOrder">确认</view>
    </view>
  </view>
</view>
