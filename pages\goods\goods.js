// goods.js
const util = require('../../utils/util.js')
const TabbarManager = require('../../utils/tabbar-manager.js')
const commApi = require('../../api/commApi.js')
const goodsApi = require('../../api/goods.js')

Page({
  data: {
    apiUrl:wx.getStorageSync('apiUrl')+'/common-api/v1/file/',
    searchKeyword: '',
    currentCategory: 'all',
    currentType: 'all', // 添加类型筛选
    sortType: 'newest', // newest, amount-asc, amount-desc
    listStyle: 'grid', // list, grid
    darkMode: false,
    // 字典数据
    categories: [],
    typeOptions: [],

    goods: [],
    filteredGoods: [],
    // 分页相关
    pageNum: 1,
    pageSize: 10,
    hasMore: true,
    loading: false
  },

  onLoad: function() {
    // 加载字典数据
    this.loadDictionaries()

    // 初始化商品列表 - 从API获取
    this.loadGoods()

    // 获取系统信息设置适当的列表样式
    wx.getSystemInfo({
      success: (res) => {
        // 如果屏幕宽度大于等于768px，默认使用网格样式
        if (res.windowWidth >= 768) {
          this.setData({
            listStyle: 'grid'
          })
        }
      }
    })
  },

  onShow: function() {
    // 更新底部tabbar选中状态
    TabbarManager.setTabbarSelected(this, 1)

    // 每次显示页面时刷新商品列表
    this.loadGoods()
  },

  onPullDownRefresh: function() {
    // 下拉刷新 - 重置分页并重新加载
    this.setData({
      pageNum: 1,
      goods: [],
      filteredGoods: []
    })
    this.loadGoods().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  onReachBottom: function() {
    // 上拉加载更多
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreGoods()
    }
  },

  onSearchInput: function(e) {
    const keyword = e.detail.value
    this.setData({
      searchKeyword: keyword,
      pageNum: 1,
      goods: [],
      filteredGoods: []
    })
    // 使用API搜索
    this.loadGoods()
  },

  clearSearch: function() {
    this.setData({
      searchKeyword: '',
      pageNum: 1,
      goods: [],
      filteredGoods: []
    })
    this.loadGoods()
  },

  switchCategory: function(e) {
    const category = e.currentTarget.dataset.category
    this.setData({
      currentCategory: category,
      pageNum: 1,
      goods: [],
      filteredGoods: []
    })
    // 使用API获取数据
    this.loadGoods()
  },

  switchSort: function(e) {
    const sortType = e.currentTarget.dataset.sort
    this.setData({
      sortType: sortType,
      pageNum: 1,
      goods: [],
      filteredGoods: []
    })
    this.loadGoods()
  },

  switchType: function(e) {
    const type = e.currentTarget.dataset.type
    this.setData({
      currentType: type,
      pageNum: 1,
      goods: [],
      filteredGoods: []
    })
    this.loadGoods()
  },

  // 免费筛选功能已移除

  toggleListStyle: function() {
    this.setData({
      listStyle: this.data.listStyle === 'list' ? 'grid' : 'list'
    })
  },





  navigateToDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    console.log('点击商品，准备跳转到详情页, ID:', id);
     
    // 检查是否已认证
    if (util.checkAuthentication()) {
      wx.navigateTo({
        url: `/profilePackage/pages/goods/detail/detail?id=${id}`
      });
    } else {
      util.showAuthModal();
    }
  },

  navigateToPublish: function() {
    // 检查是否已认证
    if (util.checkAuthentication()) {
      wx.navigateTo({
        url: '/profilePackage/pages/goods/publish/publish'
      })
    } else {
      util.showAuthModal()
    }
  },



  // 加载字典数据
  loadDictionaries: function() {
    try {
       
      // 使用统一的字典获取方法
      const typeDict = util.getDictByNameEn('good_stuff_type');
      const categoryDict = util.getDictByNameEn('good_stuff_category');

      this.setData({
        typeOptions: typeDict && typeDict.length > 0 && typeDict[0].children ? typeDict[0].children : [],
        categories: categoryDict && categoryDict.length > 0 && categoryDict[0].children ? categoryDict[0].children : []
      });

      console.log('商品字典数据加载完成:', {
        typeOptions: this.data.typeOptions,
        categories: this.data.categories
      });
    } catch (error) {
      console.error('加载商品字典数据失败:', error);
      this.setData({
        typeOptions: [],
        categories: []
      });
    }
  },

  // 加载商品列表
  loadGoods: function() {
    if (this.data.loading) return Promise.resolve()

    this.setData({ loading: true })

    const params = {
      pageNum: this.data.pageNum,
      pageSize: this.data.pageSize
    }

    // 添加搜索关键词
    if (this.data.searchKeyword) {
      params.title = this.data.searchKeyword
    }

    // 添加分类筛选
    if (this.data.currentCategory && this.data.currentCategory !== 'all') {
      params.categoryCode = this.data.currentCategory
    }

    // 添加类型筛选
    if (this.data.currentType && this.data.currentType !== 'all') {
      params.type = this.data.currentType
    }

    // 添加排序参数
    if (this.data.sortType && this.data.sortType !== 'newest') {
      if (this.data.sortType === 'amount-asc') {
        params.sortBy = 'amount'
        params.sortOrder = 'asc'
      } else if (this.data.sortType === 'amount-desc') {
        params.sortBy = 'amount'
        params.sortOrder = 'desc'
      }
    }

    return goodsApi.getPlatformGoodsList(params).then(res => {
      if ( res && res.list) {
        const { list, total, pages } = res

        // 处理商品数据
        const goodsList = list.map(item => this.normalizeGoodsData(item))

        // 如果是第一页，替换数据；否则追加数据
        const newGoods = this.data.pageNum === 1 ? goodsList : [...this.data.goods, ...goodsList]

        this.setData({
          goods: newGoods,
          filteredGoods: newGoods,
          hasMore: this.data.pageNum < pages,
          loading: false
        })

        console.log('商品列表加载成功:', goodsList)
      } else {
        console.error('商品列表加载失败:', res)
        this.setData({ loading: false })
        wx.showToast({
          title: res.message || '加载失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error('商品列表API调用失败:', err)
      this.setData({ loading: false })
   
    })
  },

  // 加载更多商品
  loadMoreGoods: function() {
    if (!this.data.hasMore || this.data.loading) return

    this.setData({
      pageNum: this.data.pageNum + 1
    })

    this.loadGoods()
  },

  // 规范化商品数据
  normalizeGoodsData: function(item) {
    // 设置商品类型名称（从字典获取）
    const typeOption = this.data.typeOptions.find(option => option.nameEn === item.type);
    const typeName = typeOption ? typeOption.nameCn : item.type;

    // 动态判断是否为免费类型
    const isFreeType = this.checkIsFreeType(item.type);

    // 处理media字段（多个图片逗号分隔）
    let images = [];
    let firstImage = '';
    if (item.media) {
      images = item.media.split(',').map(img => {
        const trimmedImg = img.trim();
        return trimmedImg.startsWith('http') ? trimmedImg : this.data.apiUrl + trimmedImg;
      });
      firstImage = images[0] || '';
    }
     
    return {
      id: item.id,
      title: item.title || '',
      stuffDescribe: item.stuffDescribe || '',
      amount: item.amount || 0,
      address: item.address || '',
      categoryCode: item.categoryCode || '',
      type: item.type,
      typeName: typeName,
      isFreeType: isFreeType,
      userName:item.userName.length>5? item.userName.substring(0,5)+'...':item.userName || '',
      sellerAvatarUrl: item.sellerAvatarUrl || null,
      userNameInitial: (item.userName && item.userName.length > 0) ? item.userName.charAt(0) : '匿',
      media: item.media || '',
      images: images,
      firstImage: firstImage,
      stock: item.stock || 1,
      views: item.views || 0,
      createTime: item.createTime,
      updateTime: item.updateTime,
      isCollect: item.isCollect || false

    }


  },

  // 动态判断是否为免费类型（基于字典配置）
  checkIsFreeType: function(type) {
    const typeOption = this.data.typeOptions.find(option => option.nameEn === type);
    // 可以根据字典中的特殊标识来判断，比如 nameEn 包含 'free' 或者有特殊字段
    return typeOption && (typeOption.nameEn.includes('free') || typeOption.nameCn.includes('免费'));
  }
})
