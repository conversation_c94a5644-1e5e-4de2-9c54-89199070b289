// 我的租客页面
const app = getApp()
const util = require('../../../../utils/util.js')
const familyApi = require('../../../../api/familyApi.js')
const houseApi = require('../../../../api/houseApi.js')
const commApi = require('../../../../api/commApi.js')
const dateUtil = require('../../../../utils/dateUtil.js')
// 引入二维码生成工具
const QR = require('../../../../utils/qrcode.js')



Page({
  data: {
    familyMembers: [],
    loading: false,
    showActionMenu: false,
    actionMenuTop: 0,
    actionMenuLeft: 0,
    currentFamilyId: '',
    showModal: false,
    isEditing: false,
    isSaving: false,
    formData: {
      id: '',
      name: '',
      phone: '',
      idCardNumber: '',
      certificateType: '',
      house: '',
      houseId: '',
      roomId: '',
      residentType:'tenant'
    },
    showHousePicker: false,
    housePickerValue: [0],
    houses: [],
    showDeleteConfirm: false,
    // 证件类型相关
    certificateTypes: [],
    showCertificateTypePicker: false,
    certificateTypePickerValue: [0],
    // 住户身份类型相关
    residentTypes: [], // 租客身份选项（租客和租客家属）
    showResidentTypePicker: false,
    residentTypePickerValue: [0],
    // 分页相关
    pageNum: 1,
    pageSize: 10,
    hasMore: true,

    // 审核相关
    showReviewModal: false,
    reviewResult: '',
    currentReviewId: '',
    residentStatusOptions: [], // 租客状态字典

    // 审核相关
    showReviewModal: false,
    reviewResult: '',
    currentReviewId: '',
    residentStatusOptions: [], // 租客状态字典

    // 二维码弹窗相关数据
    showQRCodeModal: false,
    currentFamilyData: null, // 当前生成二维码的租客数据
    qrCodeImagePath: '', // 二维码图片路径
    qrCodeGenerated: false, // 二维码是否生成成功
    qrCodeToken: '', // 二维码token
    qrCodeExpireTime: '', // 二维码过期时间
    isGeneratingQRCode: false // 是否正在生成二维码
  },

  onLoad: function (options) {
    // 加载证件类型字典
    this.loadCertificateTypes()

    // 加载住户身份选项
    this.loadResidentTypes()
    // 加载租客状态字典
    this.loadResidentStatusDict()

    // 加载房屋列表（会触发租客列表加载）
    this.loadHouses()
  },

  onShow: function () {
    // 页面显示时刷新租客列表，确保数据是最新的
    if (this.data.residentTypes.length > 0) {
      this.loadFamilyMembers(true)
    }
  },

  // 加载证件类型字典
  loadCertificateTypes: function () {
    try {
      const certificateTypeDict = util.getDictByNameEn('certificate_type');
      if (certificateTypeDict && certificateTypeDict.length > 0 && certificateTypeDict[0].children) {
        this.setData({
          certificateTypes: certificateTypeDict[0].children
        });
      } else {
        // 使用默认数据
        this.setData({
          certificateTypes: [
            { nameCn: '身份证', nameEn: 'id_card' },
            { nameCn: '护照', nameEn: 'passport' },
            { nameCn: '港澳通行证', nameEn: 'hk_macao_pass' },
            { nameCn: '台湾通行证', nameEn: 'taiwan_pass' }
          ]
        });
      }
    } catch (error) {
      console.error('加载证件类型字典失败:', error);
      // 使用默认数据
      this.setData({
        certificateTypes: [
          { nameCn: '身份证', nameEn: 'id_card' },
          { nameCn: '护照', nameEn: 'passport' },
          { nameCn: '港澳通行证', nameEn: 'hk_macao_pass' },
          { nameCn: '台湾通行证', nameEn: 'taiwan_pass' }
        ]
      });
    }
  },

  // 加载住户身份选项（只包含租客和租客家属）
  loadResidentTypes: function () {
    try {
      const residentTypeDict = util.getDictByNameEn('resident_type');
      if (residentTypeDict && residentTypeDict.length > 0 && residentTypeDict[0].children) {
        // 过滤出租客和租客家属选项
        const residentOptions = residentTypeDict[0].children.filter(item =>
          item.nameEn === 'tenant' || item.nameEn === 'tenant_family'
        );
        this.setData({
          residentTypes: residentOptions
        });
        console.log('租客身份选项:', residentOptions);
      } else {
        console.warn('住户身份字典数据为空');
        this.setData({
          residentTypes: []
        });
      }
    } catch (error) {
      console.error('加载住户身份选项失败:', error);
      this.setData({
        residentTypes: []
      });
    }
  },

  // 加载租客状态字典
  loadResidentStatusDict: function () {
    try {
      const residentStatusDict = util.getDictByNameEn('resident_status');
      if (residentStatusDict && residentStatusDict.length > 0 && residentStatusDict[0].children) {
        this.setData({
          residentStatusOptions: residentStatusDict[0].children
        });
        console.log('租客状态选项:', residentStatusDict[0].children);
      } else {
        console.warn('租客状态字典数据为空');
        this.setData({
          residentStatusOptions: []
        });
      }
    } catch (error) {
      console.error('加载租客状态字典失败:', error);
      this.setData({
        residentStatusOptions: []
      });
    }
  },

  // 加载租客列表
  loadFamilyMembers: function (refresh = false) {
    if (refresh) {
      this.setData({
        pageNum: 1,
        familyMembers: [],
        hasMore: true
      })
    }

    if (this.data.loading || !this.data.hasMore) {
      return
    }

    this.setData({ loading: true })

    var params = {
      pageNum: this.data.pageNum,
      pageSize: this.data.pageSize,
      communityId: wx.getStorageSync('selectedCommunity').id,
      residentTypes:'tenant,tenant_family'
    }
    familyApi.getFamilyList(params).then(res => {
       
      console.log('租客列表数据：', res)
       
      if ( res && res.list) {

        const newMembers = res.list.map(member => {
          // 获取名字首字母
          const nameInitial = member.residentName ? member.residentName.charAt(0) : '?'

          // 格式化房屋信息
          const house = this.formatHouseInfo(member)

          // 根据residentType使用字典显示关系文本
          let relationText = '租客'; // 默认值
          let relationClass = 'relation-tenant'; // 默认样式

          // 从字典中查找对应的显示文本
          const residentTypeOption = this.data.residentTypes.find(type =>
            type.nameEn === member.residentType
          );

          if (residentTypeOption) {
            relationText = residentTypeOption.nameCn;
            // 根据类型设置样式类
            relationClass = member.residentType === 'tenant_family' ? 'relation-tenant-family' : 'relation-tenant';
          }

          // 获取租客状态显示文本
          let statusText = '';
          let statusClass = '';
          if (member.status) {
            const statusOption = this.data.residentStatusOptions.find(status =>
              status.nameEn === member.status
            );
            if (statusOption) {
              statusText = statusOption.nameCn;
              statusClass = `status-${member.status}`;
            }
          }

          return {
            id: member.id,
            name: member.residentName,
            phone: member.phone,
            idCardNumber: member.idCardNumber,
            certificateType: member.certificateType,
            roomId: member.roomId,
            residentId: member.residentId,
            residentType: member.residentType || 'tenant', // 保存住户身份类型
            status: member.status || 'pending', // 租客状态
            nameInitial,
            house,
            relationText,
            relationClass,
            statusText,
            statusClass,
            showActions: false // 控制操作按钮显示
          }
        })

        const allMembers = refresh ? newMembers : [...this.data.familyMembers, ...newMembers]

        this.setData({
          familyMembers: allMembers,
          pageNum: this.data.pageNum + 1,
          hasMore: newMembers.length === this.data.pageSize,
          loading: false
        })

        // 检查是否有房屋信息显示为"房间X"的情况，如果有则延迟重新格式化
        const hasIncompleteAddress = allMembers.some(member =>
          member.house && member.house.startsWith('房间') && this.data.houses.length > 0
        );

        if (hasIncompleteAddress) {
          console.log('检测到不完整的地址信息，重新格式化');
          setTimeout(() => {
            this.refreshHouseInfo();
          }, 500);
        }

        wx.setStorageSync('tenant_members', allMembers)
      } else{
        this.setData({
          
          loading: false
        })
      }
    }).catch(err => {
       
      console.error('获取租客列表异常：', err)
      this.setData({ loading: false })
 
    })
  },

  // 格式化房屋信息
  formatHouseInfo: function (member) {
    if (!member.roomId) {
      return ''
    }

    // 从房屋列表中查找对应的房屋信息
    const house = this.data.houses.find(h => h.roomId === member.roomId)
    if (house) {
      return house.fullAddress || house.address || `房间${member.roomId}`
    }

    return `房间${member.roomId}`
  },

  // 加载房屋列表
  loadHouses: function () {
    const selectedCommunity = wx.getStorageSync('selectedCommunity');
    if (!selectedCommunity || !selectedCommunity.id) {
      console.log('未选择小区，跳过房屋数据加载');
      return;
    }

    const params = {
      pageNum: 1,
      pageSize: 100,
      communityId: selectedCommunity.id
    };
    houseApi.getHouseList(params).then(res => {
      console.log('房屋列表数据：', res)

      if (res && Array.isArray(res.list)) {
        const houses = res.list.map(house => ({
          id: house.id,
          roomId: house.roomId,
          fullAddress: this.formatHouseAddress(house),
          address: this.formatHouseAddress(house),
          isDefault: house.isDefault || false
        }))

        // 按默认房屋优先排序
        const sortedHouses = houses.sort((a, b) => {
          if (a.isDefault && !b.isDefault) return -1
          if (!a.isDefault && b.isDefault) return 1
          return 0
        })

        this.setData({
          houses: sortedHouses
        })

        // 如果租客列表已经加载，重新格式化房屋信息
        if (this.data.familyMembers.length > 0) {
          this.refreshHouseInfo();
        }

        // 加载租客列表
        this.loadFamilyMembers()



      } else {
        console.error('获取房屋列表失败：', res)

      }
    }).catch(err => {
      console.error('获取房屋列表异常：', err)

    })
  },

  // 格式化房屋地址
  formatHouseAddress: function (house) {
    const parts = []
    if (house.buildingNumber) parts.push(house.buildingNumber)
    if (house.unitNumber) parts.push(house.unitNumber)
    if (house.roomNumber) parts.push(house.roomNumber)
    return parts.join(' ') || `房间${house.roomId}`
  },

  // 重新格式化房屋信息
  refreshHouseInfo: function() {
    console.log('重新格式化房屋信息');
    const updatedMembers = this.data.familyMembers.map(member => {
      const updatedHouse = this.formatHouseInfo(member);
      return {
        ...member,
        house: updatedHouse
      };
    });

    this.setData({
      familyMembers: updatedMembers
    });

    // 更新缓存
    wx.setStorageSync('tenant_members', updatedMembers);
  },


  // 切换操作按钮显示
  showActionMenu: function (e) {
    const id = e.currentTarget.dataset.id
    const updatedMembers = this.data.familyMembers.map(member => {
      if (member.id === id) {
        return { ...member, showActions: !member.showActions }
      } else {
        return { ...member, showActions: false } // 关闭其他项的操作按钮
      }
    })

    this.setData({
      familyMembers: updatedMembers,
      currentFamilyId: id
    })
  },

  // 显示审核弹窗
  showReviewModal: function (e) {
    const id = e.currentTarget.dataset.id
    this.setData({
      showReviewModal: true,
      currentReviewId: id,
      reviewResult: ''
    })
  },

  // 隐藏审核弹窗
  hideReviewModal: function () {
    this.setData({
      showReviewModal: false,
      currentReviewId: '',
      reviewResult: ''
    })
  },

  // 选择审核结果
  selectReviewResult: function (e) {
    const result = e.currentTarget.dataset.result
    this.setData({
      reviewResult: result
    })
  },

  // 显示邀请二维码
  showInviteQRCode: function (e) {
    const id = e.currentTarget.dataset.id
    const member = this.data.familyMembers.find(m => m.id === id)

    if (member) {
      // 这里可以调用生成二维码的方法
      wx.showToast({
        title: '二维码功能开发中',
        icon: 'none'
      })
    }
  },

  // 确认审核
  confirmReview: function () {
    if (!this.data.reviewResult) {
      wx.showToast({
        title: '请选择审核结果',
        icon: 'none'
      })
      return
    }

    const params = {
      id: this.data.currentReviewId,
      status: this.data.reviewResult
    }

    wx.showLoading({ title: '审核中...' })

    wx.request({
      url: wx.getStorageSync('apiUrl') + '/users-api/v1/member/resident/children/examine',
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + wx.getStorageSync('token'),
        'Content-Type': 'application/json',
        'Community-Id': wx.getStorageSync('selectedCommunity').id
      },
      data: params,
      success: (res) => {
        wx.hideLoading()
        if (res.statusCode === 200 && res.data.code === 200) {
          wx.showToast({
            title: '审核成功',
            icon: 'success'
          })
          this.hideReviewModal()
          // 刷新列表
          this.loadFamilyMembers(true)
        } else {
          wx.showToast({
            title: res.data.message || '审核失败',
            icon: 'none'
          })
        }
      },
      fail: (error) => {
        wx.hideLoading()
        console.error('审核失败:', error)
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      }
    })
  },

  // 编辑租客信息
  editFamilyMember: function (e) {
    const id = e.currentTarget.dataset.id
    const member = this.data.familyMembers.find(m => m.id === id)

    if (member) {
      // 查找房屋在列表中的索引
      const houseIndex = this.data.houses.findIndex(h => h.roomId === member.roomId)

      // 如果找到对应房屋，使用房屋列表中的地址信息；否则使用成员原有的房屋信息
      let houseDisplayText = member.house || ''
      let houseId = member.houseId || ''
      let roomId = member.roomId || ''

      if (houseIndex > -1) {
        const foundHouse = this.data.houses[houseIndex]
        houseDisplayText = foundHouse.fullAddress || foundHouse.address
        houseId = foundHouse.id
        roomId = foundHouse.roomId
      }
       
      // 查找住户身份类型在列表中的索引
      const residentTypeIndex = this.data.residentTypes.findIndex(type =>
        type.nameEn === (member.residentType || 'tenant')
      );

      this.setData({
        isEditing: true,
        formData: {
          id: member.id,
          name: member.name,
          phone: member.phone || '',
          idCardNumber: member.idCardNumber || '',
          certificateType: member.certificateType || 'id_card',
          house: houseDisplayText,
          houseId: houseId,
          roomId: roomId,
          residentId: member.residentId || '',
          residentType: member.residentType || 'tenant'
        },

        housePickerValue: [houseIndex > -1 ? houseIndex : 0],
        residentTypePickerValue: [residentTypeIndex > -1 ? residentTypeIndex : 0],
        showActionMenu: false,
        showModal: true
      })
    }
  },

  // 显示删除确认弹窗
  showDeleteConfirm: function (e) {
    const id = e.currentTarget.dataset.id
    this.setData({
      showDeleteConfirm: true,
      currentFamilyId: id
    })
  },

  // 隐藏删除确认弹窗
  hideDeleteConfirm: function () {
    this.setData({
      showDeleteConfirm: false
    })
  },

  // 删除租客信息
  deleteFamilyMember: function () {
    const id = this.data.currentFamilyId

    wx.showLoading({
      title: '删除中...'
    })

    familyApi.deleteFamily(id).then(res => {
      wx.hideLoading()

  
        // 从列表中移除
        const familyMembers = this.data.familyMembers.filter(m => m.id !== id)

        this.setData({
          familyMembers,
          showDeleteConfirm: false
        })

        wx.showToast({
          title: '删除成功',
          icon: 'success'
        })
    
    }).catch(err => {
      wx.hideLoading()
      console.error('删除租客异常：', err)

    })
  },

  // 显示添加租客弹窗
  showAddFamilyModal: function () {
    // 设置默认房屋选择（选择第一个房屋）
    let defaultHouse = ''
    let defaultHouseId = ''
    let defaultRoomId = ''
    let defaultPickerValue = [0]

    if (this.data.houses && this.data.houses.length > 0) {
      const firstHouse = this.data.houses[0]
      defaultHouse = firstHouse.fullAddress || firstHouse.address
      defaultHouseId = firstHouse.id
      defaultRoomId = firstHouse.roomId
    }

    // 设置默认证件类型（选择第一个证件类型）
    let defaultCertificateType = ''
    let defaultCertificateTypePickerValue = [0]

    if (this.data.certificateTypes && this.data.certificateTypes.length > 0) {
      defaultCertificateType = this.data.certificateTypes[0].nameEn
    }

    // 设置默认住户身份类型（选择第一个住户身份类型）
    let defaultResidentType = 'tenant'
    let defaultResidentTypePickerValue = [0]

    if (this.data.residentTypes && this.data.residentTypes.length > 0) {
      defaultResidentType = this.data.residentTypes[0].nameEn
    }

    this.setData({
      isEditing: false,
      formData: {
        id: '',
        name: '',
        phone: '',
        idCardNumber: '',
        certificateType: defaultCertificateType,
        house: defaultHouse,
        houseId: defaultHouseId,
        roomId: defaultRoomId,
        residentType: defaultResidentType
      },
      housePickerValue: defaultPickerValue,
      certificateTypePickerValue: defaultCertificateTypePickerValue,
      residentTypePickerValue: defaultResidentTypePickerValue,
      showModal: true
    })
  },

  // 关闭弹窗
  closeModal: function () {
    this.setData({
      showModal: false,
      showImportPanel: false,
      showCertificateTypePicker: false,
      showResidentTypePicker: false
    })
  },

  // 显示证件类型选择器
  showCertificateTypePicker: function () {
    this.setData({
      showCertificateTypePicker: true
    })
  },

  // 隐藏证件类型选择器
  hideCertificateTypePicker: function () {
    this.setData({
      showCertificateTypePicker: false
    })
  },

  // 证件类型选择变化
  onCertificateTypeChange: function (e) {
    const index = e.detail.value[0]
    const certificateTypes = this.data.certificateTypes

    if (index >= 0 && index < certificateTypes.length) {
      const selectedType = certificateTypes[index]
      this.setData({
        'formData.certificateType': selectedType.nameEn,
        certificateTypePickerValue: [index],
        showCertificateTypePicker: false
      })
    }
  },

  // 显示住户身份选择器
  showResidentTypePicker: function () {
    this.setData({
      showResidentTypePicker: true
    })
  },

  // 隐藏住户身份选择器
  hideResidentTypePicker: function () {
    this.setData({
      showResidentTypePicker: false
    })
  },

  // 住户身份选择变化
  onResidentTypeChange: function (e) {
    const index = e.detail.value[0]
    const residentTypes = this.data.residentTypes

    if (index >= 0 && index < residentTypes.length) {
      const selectedType = residentTypes[index]

      this.setData({
        'formData.residentType': selectedType.nameEn,
        residentTypePickerValue: [index],
        showResidentTypePicker: false
      })
    }
  },

  // 防止滑动穿透
  preventTouchMove: function () {
    return false
  },

  // 阻止事件冒泡
  stopPropagation: function () {
    // 阻止事件冒泡
  },

  // 表单输入处理
  onNameInput: function (e) {
    this.setData({
      'formData.name': e.detail.value
    })
  },

  onPhoneInput: function (e) {
    this.setData({
      'formData.phone': e.detail.value
    })
  },

  onIdCardInput: function (e) {
    this.setData({
      'formData.idCardNumber': e.detail.value
    })
  },



  // 显示房屋选择器
  showHousePicker: function () {
    if (this.data.houses.length === 0) {
      wx.showToast({
        title: '暂无房屋信息',
        icon: 'none'
      })
      return
    }

    this.setData({
      showHousePicker: true
    })
  },

  // 隐藏房屋选择器
  hideHousePicker: function () {
    // 确保当前选中的房屋被保存到formData中
    const currentIndex = this.data.housePickerValue[0] || 0
    const houses = this.data.houses

    if (houses && houses.length > 0 && currentIndex < houses.length) {
      const selectedHouse = houses[currentIndex]
      this.setData({
        'formData.houseId': selectedHouse.id,
        'formData.roomId': selectedHouse.roomId,
        'formData.house': selectedHouse.fullAddress || selectedHouse.address
      })
    }

    this.setData({
      showHousePicker: false
    })
  },

  // 房屋选择变化
  onHouseChange: function (e) {
    const index = e.detail.value[0]
    const house = this.data.houses[index]
    this.setData({
      'formData.houseId': house.id,
      'formData.roomId': house.roomId,
      'formData.house': house.fullAddress || house.address,
      housePickerValue: [index]
    })
  },



  // 保存租客信息
  saveFamilyMember: function () {
    // 表单验证
    if (!this.data.formData.name) {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      })
      return
    }

    // 设置保存中状态
    this.setData({
      isSaving: true
    })

    const formData = this.data.formData
    const familyData = {
      residentName: formData.name,
      phone: formData.phone,
      idCardNumber: formData.idCardNumber,
      certificateType: formData.certificateType,
      roomId: formData.roomId || null,
      residentType: formData.residentType || 'tenant'
    }
     
    let apiCall
    if (this.data.isEditing)
     {
        
      // 编辑模式
      familyData.id = formData.id
      familyData.familyResidentId = formData.residentId
      
      apiCall = familyApi.updateFamily(familyData)
    } else {
      // 新增模式
      apiCall = familyApi.addFamily(familyData)
    }

    apiCall.then(res => {
      console.log('保存租客信息结果：', res)

     
        // 重新加载租客列表
        this.loadFamilyMembers(true)

        // 关闭弹窗
        this.closeModal()

        wx.showToast({
          title: this.data.isEditing ? '更新成功' : '添加成功',
          icon: 'success'
        })
    
    }).catch(err => {
      console.error('保存租客信息异常：', err)
     
    }).finally(() => {
      // 重置保存状态
      this.setData({
        isSaving: false
      })
    })
  },

  // 下拉刷新
  onPullDownRefresh: function () {
     
    this.loadFamilyMembers(true)
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  // 上拉加载更多
  onReachBottom: function () {
    this.loadFamilyMembers()
  },

  // ==================== 邀请二维码相关方法 ====================

  // 显示邀请二维码
  showInviteQRCode: function () {
    const id = this.data.currentFamilyId;
    const familyData = this.data.familyMembers.find(m => m.id === id);

    if (!familyData) {
      wx.showToast({
        title: '租客信息不存在',
        icon: 'none'
      });
      return;
    }

    // 重置二维码相关状态
    this.setData({
      showQRCodeModal: true,
      currentFamilyData: familyData,
      qrCodeGenerated: false,
      qrCodeImagePath: '',
      qrCodeToken: '',
      qrCodeExpireTime: '',
      isGeneratingQRCode: false,
      showActionMenu: false // 隐藏操作菜单
    });
  },

  // 隐藏邀请二维码弹窗
  hideInviteQRCode: function () {
    this.setData({
      showQRCodeModal: false,
      currentFamilyData: null,
      qrCodeGenerated: false,
      qrCodeImagePath: '',
      qrCodeToken: '',
      qrCodeExpireTime: '',
      isGeneratingQRCode: false
    });
  },

  // 生成邀请二维码
  generateInviteQRCode: function () {
    if (!this.data.currentFamilyData) {
      wx.showToast({
        title: '租客信息不存在',
        icon: 'none'
      });
      return;
    }

    // 设置生成中状态，清除旧的二维码
    this.setData({
      isGeneratingQRCode: true,
      qrCodeGenerated: false,
      qrCodeImagePath: '',
      qrCodeToken: '',
      qrCodeExpireTime: ''
    });

    console.log('调用租客邀请二维码API，租客ID:', this.data.currentFamilyData.id);
    console.log('租客住户身份类型:', this.data.currentFamilyData.residentType);

    commApi.createQrCodeWithOne(this.data.currentFamilyData.id)
      .then(res => {
        console.log('租客邀请二维码API返回:', res);

        if (res) {
          const { token, expireIn } = res;

          // 计算过期时间
          const expireTime = new Date(Date.now() + expireIn * 1000);
          const formattedExpireTime = dateUtil.formatDateTime(expireTime);

          // 保存token和过期时间
          this.setData({
            qrCodeToken: token,
            qrCodeExpireTime: formattedExpireTime
          });

          // 生成二维码内容
          const qrContent = `roomCode:${this.data.currentFamilyData.id}:${token}`;
          console.log('生成租客邀请二维码内容:', qrContent);

          // 设置画布大小并生成二维码
          const size = this.setCanvasSize();
          this.createQrCode(qrContent, "tenantQrCanvas", size.w, size.h);

        } else {
          wx.showToast({
            title: res.errorMessage || '获取二维码失败',
            icon: 'none'
          });
          this.setData({
            isGeneratingQRCode: false
          });
        }
      })
      .catch(err => {
        console.error('调用租客邀请二维码API异常:', err);
   
        this.setData({
          isGeneratingQRCode: false
        });
      });
  },

  // 适配不同屏幕大小的canvas
  setCanvasSize: function () {
    var size = {};
    try {
      // 固定使用合适的二维码尺寸，确保生成质量
      size.w = 300;
      size.h = 300;
    } catch (e) {
      console.log("获取设备信息失败" + e);
      // 设置默认尺寸
      size.w = 300;
      size.h = 300;
    }
    return size;
  },

  // 创建二维码
  createQrCode: function (url, canvasId, cavW, cavH) {
    console.log('开始创建租客邀请二维码:', {
      url: url,
      canvasId: canvasId,
      width: cavW,
      height: cavH
    });

    const that = this;

    // 调用插件中的draw方法，绘制二维码图片
    setTimeout(() => {
      try {
        QR.api.draw(url, canvasId, cavW, cavH, this, function() {
          console.log('租客邀请二维码绘制完成，开始转换为图片');
          that.canvasToTempImage();
        });
      } catch (error) {
        console.error('租客邀请二维码生成失败:', error);
        that.setData({
          isGeneratingQRCode: false
        });
        wx.showToast({
          title: '二维码生成失败',
          icon: 'none'
        });
      }
    }, 300);
  },

  // 获取临时缓存照片路径，存入data中
  canvasToTempImage: function () {
    const that = this;
    wx.canvasToTempFilePath({
      canvasId: 'tenantQrCanvas',
      x: 0,
      y: 0,
      width: 300,
      height: 300,
      destWidth: 300,
      destHeight: 300,
      success: function (res) {
        const tempFilePath = res.tempFilePath;
        console.log('租客邀请二维码生成成功:', tempFilePath);
        that.setData({
          qrCodeImagePath: tempFilePath,
          qrCodeGenerated: true,
          isGeneratingQRCode: false
        });
      },
      fail: function (res) {
        console.log('生成租客邀请二维码图片失败:', res);
        wx.showToast({
          title: '二维码生成失败',
          icon: 'none'
        });
        that.setData({
          isGeneratingQRCode: false
        });
      }
    }, this);
  },

  // 预览二维码图片
  previewQRCode: function () {
    if (this.data.qrCodeImagePath) {
      wx.previewImage({
        current: this.data.qrCodeImagePath,
        urls: [this.data.qrCodeImagePath]
      });
    }
  },

  // 保存二维码到相册
  saveQRCodeToAlbum: function () {
    if (!this.data.qrCodeImagePath) {
      wx.showToast({
        title: '二维码未生成',
        icon: 'none'
      });
      return;
    }

    wx.saveImageToPhotosAlbum({
      filePath: this.data.qrCodeImagePath,
      success: function () {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });
      },
      fail: function (err) {
        if (err.errMsg === 'saveImageToPhotosAlbum:fail auth deny') {
          wx.showModal({
            title: '提示',
            content: '需要您授权保存相册',
            showCancel: false,
            confirmText: '确定'
          });
        } else {
          wx.showToast({
            title: '保存失败',
            icon: 'none'
          });
        }
      }
    });
  }

})
