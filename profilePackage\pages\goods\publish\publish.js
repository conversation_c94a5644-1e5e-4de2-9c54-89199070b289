// 发布商品页
const util = require('@/utils/util.js')
const commApi = require('@/api/commApi.js')
const goodsApi = require('@/api/goods.js')
const app = getApp()

Page({
  data: {
    apiUrl: wx.getStorageSync('apiUrl') + '/common-api/v1/file/',
    darkMode: false,
    isEdit: false, // 是否是编辑模式
    goodsId: null, // 编辑时的商品ID
    isAgreed: false, // 是否同意发布须知
    // 富文本编辑器相关
    showRichEditor: false, // 是否显示富文本编辑器
    editorContext: null, // 编辑器上下文
    editorFormats: {}, // 当前格式状态
    currentTextColor: '#333333', // 当前文字颜色
    descriptionLength: 0, // 描述长度
    formData: {
      title: '', // 好物名称
      stuffDescribe: '', // 商品描述
      amount: '', // 价格
      categoryCode: '', // 分类代码
      type: '', // 商品类型
      stock: '1', // 库存
      media: [], // 图片数组
      address: '', // 交易地点
      points: '0', // 积分（保留但隐藏）
      orderExpireTime: 1, // 订单超时时间数量（正整数）
      timeUnit: 'hour' // 时间单位
    },
    // 字典数据
    typeOptions: [], // 好物类型字典
    categoryOptions: [], // 好物分类字典
    typeIndex: 0,
    categoryIndex: -1,
    selectedCategoryName: '',
    // 时间单位选项
    timeUnitOptions: [],
    timeUnitIndex: 0 // 默认选择小时
  },

  onLoad: function (options) {
    // 检查是否已认证
    if (!util.checkAuthentication()) {
      util.showAuthModal()
      return
    }
    /**
     * 计算两个数字的和
     * @param {number} a - 第一个加数
     * @param {number} b - 第二个加数
     * @returns {number} 两个参数的和
     */

    // 加载字典数据
    this.loadDictionaries()

    // 检查是否是编辑模式
    if (options.id) {
      this.setData({
        isEdit: true,
        goodsId: options.id
      })
      this.loadGoodsDetail(options.id)
    }
  },

  onShow: function () {
    // 检查暗黑模式
    this.setData({
      darkMode: app.globalData.darkMode || false
    })

    // 监听暗黑模式变化
    if (app.globalData.darkModeChangeEvent) {
      this.setData({
        darkMode: app.globalData.darkModeChangeEvent.darkMode
      })
    }
  },

  // 加载字典数据
  loadDictionaries: function () {
    try {
      // 使用统一的字典获取方法
      const typeDict = util.getDictByNameEn('good_stuff_type');
      const categoryDict = util.getDictByNameEn('good_stuff_category');
      const timeUnitDict = util.getDictByNameEn('time_unit');

      // 处理时间单位选项
      let timeUnitOptions = [];
      if (timeUnitDict && timeUnitDict.length > 0 && timeUnitDict[0].children) {
        timeUnitOptions = timeUnitDict[0].children.map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }));
      } else {
        // 字典为空时使用默认值
        timeUnitOptions = [
          { label: '小时', value: 'hour' },
          { label: '天', value: 'day' }
        ];
      }

      this.setData({
        typeOptions: typeDict && typeDict.length > 0 && typeDict[0].children ? typeDict[0].children : [],
        categoryOptions: categoryDict && categoryDict.length > 0 && categoryDict[0].children ? categoryDict[0].children : [],
        timeUnitOptions: timeUnitOptions
      });

      console.log('发布页字典数据加载完成:', {
        typeOptions: this.data.typeOptions,
        categoryOptions: this.data.categoryOptions,
        timeUnitOptions: this.data.timeUnitOptions
      });
    } catch (error) {
      console.error('加载发布页字典数据失败:', error);
      this.setData({
        typeOptions: [],
        categoryOptions: [],
        timeUnitOptions: [
          { label: '小时', value: 'hour' },
          { label: '天', value: 'day' }
        ]
      });
    }
  },


  // 加载商品详情（编辑模式）
  loadGoodsDetail: function (id) {
    wx.showLoading({
      title: '加载中...',
      mask: true
    })

    // 使用我的商品详情接口
    goodsApi.getPlatformGoodsDetail(id).then(res => {
      if ( res) {
        const goods = res

        // 处理图片数据
        let media = []
        if (goods.media && typeof goods.media === 'string') {
          media = goods.media.split(',').map(img => img.trim()).filter(img => img)
        }

        // 设置表单数据
        const formData = {
          title: goods.title || '',
          stuffDescribe: goods.stuffDescribe || '',
          amount: goods.amount ? goods.amount.toString() : '',
          categoryCode: goods.categoryCode || '',
          type: goods.type || 'free',
          stock: goods.stock ? goods.stock.toString() : '1',
          media: media,
          address: goods.address || '',
          points: goods.points ? goods.points.toString() : '0',
          orderExpireTime: goods.orderExpireTime || 1,
          timeUnit: goods.timeUnit || 'hour'
        }

        // 设置选择器索引
        const typeIndex = this.data.typeOptions.findIndex(item => item.nameEn === goods.type)
        const categoryIndex = this.data.categoryOptions.findIndex(item => item.nameEn === goods.categoryCode)
        const timeUnitIndex = this.data.timeUnitOptions.findIndex(item => item.value === goods.timeUnit)

        this.setData({
          formData,
          typeIndex: typeIndex !== -1 ? typeIndex : 0,
          categoryIndex: categoryIndex !== -1 ? categoryIndex : -1,
          selectedCategoryName: categoryIndex !== -1 ? this.data.categoryOptions[categoryIndex].nameCn : '',
          timeUnitIndex: timeUnitIndex !== -1 ? timeUnitIndex : 0
        })
      } else {
        throw new Error(res.message || '获取商品详情失败');
      }
    }).catch(err => {
      console.error('加载商品详情失败:', err);
    
    }).finally(() => {
      wx.hideLoading()
    })
  },

  // 选择图片
  chooseImage: function () {
    wx.chooseMedia({
      count: 9 - this.data.formData.media.length,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 上传图片
        this.uploadImages(res.tempFiles)
      }
    })
  },

  // 上传图片
  uploadImages: function (tempFiles) {
    if (!tempFiles || tempFiles.length === 0) return

    const uploadPromises = tempFiles.map(file => {
      return commApi.upLoadFile(file.tempFilePath)
    })

    Promise.all(uploadPromises)
      .then(results => {
        console.log('图片上传结果：', results);

        // 分别处理每个上传结果
        const successUrls = [];
        let failCount = 0;

        results.forEach((result, index) => {
          if (result && result.code === 0 && result.data) {
            // 上传成功，添加到成功列表
            successUrls.push(result.data);
          } else {
            // 上传失败，记录失败数量
            failCount++;
            console.error(`第${index + 1}张图片上传失败:`, result);
          }
        });

        // 更新成功上传的图片列表
        if (successUrls.length > 0) {
          this.setData({
            'formData.media': [...this.data.formData.media, ...successUrls]
          });
        }

        // 显示上传结果提示
        if (failCount > 0 && successUrls.length > 0) {
          wx.showToast({
            title: `${successUrls.length}张上传成功，${failCount}张失败`,
            icon: 'none',
            duration: 2000
          });
        } else if (failCount > 0) {
          wx.showToast({
            title: `${failCount}张图片上传失败`,
            icon: 'none',
            duration: 2000
          });
        } else if (successUrls.length > 0) {
          wx.showToast({
            title: `${successUrls.length}张图片上传成功`,
            icon: 'success',
            duration: 1500
          });
        }
      })
      .catch(err => {
        console.error('图片上传失败:', err.errorMessage)
 
      })
  },

  // 删除图片
  deleteImage: function (e) {
    const index = e.currentTarget.dataset.index
    const media = [...this.data.formData.media]
    media.splice(index, 1)

    this.setData({
      'formData.media': media
    })
  },

  // 输入标题
  inputTitle: function (e) {
    this.setData({
      'formData.title': e.detail.value
    })
  },

  // 输入描述
  inputDescription: function (e) {
    const value = e.detail.value;
    this.setData({
      'formData.stuffDescribe': value,
      descriptionLength: value.length
    })
  },

  // ==================== 富文本编辑器相关方法 ====================

  // 切换编辑器模式
  toggleEditor: function() {
    const showRichEditor = !this.data.showRichEditor;
    this.setData({
      showRichEditor: showRichEditor
    });

    if (showRichEditor && this.data.editorContext) {
      // 如果切换到富文本模式，将普通文本内容设置到编辑器
      const plainText = this.data.formData.stuffDescribe;
      if (plainText) {
        this.data.editorContext.setContents({
          html: `<p>${plainText}</p>`
        });
      }
    }
  },

  // 编辑器初始化完成
  onEditorReady: function() {
    const that = this;
    wx.createSelectorQuery().select('.rich-editor').context(function (res) {
      that.setData({
        editorContext: res.context
      });

      // 如果有初始内容，设置到编辑器
      if (that.data.formData.stuffDescribe) {
        res.context.setContents({
          html: `<p>${that.data.formData.stuffDescribe}</p>`
        });
      }
    }).exec();
  },

  // 编辑器内容变化
  onEditorInput: function(e) {
    const { html, text } = e.detail;
    this.setData({
      'formData.stuffDescribe': html,
      descriptionLength: text.length
    });
  },

  // 编辑器状态变化
  onEditorStatusChange: function(e) {
    this.setData({
      editorFormats: e.detail
    });
  },

  // 格式化：加粗
  formatBold: function() {
    if (this.data.editorContext) {
      this.data.editorContext.format('bold');
    }
  },

  // 格式化：斜体
  formatItalic: function() {
    if (this.data.editorContext) {
      this.data.editorContext.format('italic');
    }
  },

  // 格式化：下划线
  formatUnderline: function() {
    if (this.data.editorContext) {
      this.data.editorContext.format('underline');
    }
  },

  // 显示颜色选择器
  showColorPicker: function() {
    const colors = ['#333333', '#ff0000', '#00ff00', '#0000ff', '#ff8c00', '#800080'];
    wx.showActionSheet({
      itemList: ['黑色', '红色', '绿色', '蓝色', '橙色', '紫色'],
      success: (res) => {
        const color = colors[res.tapIndex];
        this.setData({
          currentTextColor: color
        });
        if (this.data.editorContext) {
          this.data.editorContext.format('color', color);
        }
      }
    });
  },

  // 显示字体大小选择器
  showSizePicker: function() {
    const sizes = ['12px', '14px', '16px', '18px', '20px', '24px'];
    wx.showActionSheet({
      itemList: ['小号', '正常', '中号', '大号', '特大', '超大'],
      success: (res) => {
        const size = sizes[res.tapIndex];
        if (this.data.editorContext) {
          this.data.editorContext.format('fontSize', size);
        }
      }
    });
  },

  // 选择商品类型
  selectType: function (e) {
    const type = e.currentTarget.dataset.type

    this.setData({
      'formData.type': type
    })

    // 如果选择免费送，设置价格为0并禁用输入
    if (type === 'free') {
      this.setData({
        'formData.amount': '0.00'
      })
    }
  },

  // 选择分类
  categoryChange: function (e) {
    const index = e.detail.value
    const selectedCategory = this.data.categoryOptions[index]

    if (selectedCategory) {
      this.setData({
        categoryIndex: index,
        'formData.categoryCode': selectedCategory.nameEn,
        selectedCategoryName: selectedCategory.nameCn
      })
    }
  },

  // 库存步进器
  increaseStock: function () {
    let stock = parseInt(this.data.formData.stock) || 1
    stock += 1

    this.setData({
      'formData.stock': stock.toString()
    })
  },

  decreaseStock: function () {
    let stock = parseInt(this.data.formData.stock) || 1
    if (stock > 1) {
      stock -= 1

      this.setData({
        'formData.stock': stock.toString()
      })
    }
  },

  // 选择位置
  chooseLocation: function () {

    wx.chooseLocation({
      latitude: 31,
      longitude: 120,
      success: (res) => {

        this.setData({
          'formData.address': res.name || res.address
        })
      }
    })
  },

  // 联系方式多选方法已删除

  // 联系方式相关方法已删除

  // 同意发布须知
  agreeChange: function (e) {
    this.setData({
      isAgreed: e.detail.value.includes('agree')
    })
  },

  // 输入价格
  inputPrice: function (e) {
    var amount = e.detail.value > 9999999999 ? 9999999999 : e.detail.value
    this.setData({
      'formData.amount': amount
    })
  },

  // 输入库存
  inputStock: function (e) {
    var stock = e.detail.value > 9999 ? 9999 : e.detail.value
    this.setData({
      'formData.stock': stock
    })
  },

  // 订单超时时间步进器
  increaseOrderExpireTime: function () {
    let orderExpireTime = parseInt(this.data.formData.orderExpireTime) || 1
    orderExpireTime += 1

    this.setData({
      'formData.orderExpireTime': orderExpireTime
    })
  },

  decreaseOrderExpireTime: function () {
    let orderExpireTime = parseInt(this.data.formData.orderExpireTime) || 1
    if (orderExpireTime > 1) {
      orderExpireTime -= 1

      this.setData({
        'formData.orderExpireTime': orderExpireTime
      })
    }
  },

  // 输入订单超时时间（只允许正整数）
  inputOrderExpireTime: function (e) {
    let value = e.detail.value

    // 移除小数点和负号，只保留正整数
    value = value.replace(/[^\d]/g, '')

    // 转换为整数
    let intValue = parseInt(value) || 1
    if (intValue < 1) intValue = 1
    if (intValue > 9999) intValue = 9999

    this.setData({
      'formData.orderExpireTime': intValue
    })
  },

  // 选择时间单位
  timeUnitChange: function (e) {
    const index = e.detail.value
    const selectedUnit = this.data.timeUnitOptions[index]

    if (selectedUnit) {
      this.setData({
        timeUnitIndex: index,
        'formData.timeUnit': selectedUnit.value
      })
    }
  },




  // 输入交易地点
  inputAddress: function (e) {

    this.setData({
      'formData.address': e.detail.value
    })
  },

  // 输入积分
  inputPoints: function (e) {
    this.setData({
      'formData.points': e.detail.value
    })
  },

  // 联系方式选择相关方法已删除

  // 积分相关方法已删除（积分设置已隐藏）

  // 取消发布
  cancelPublish: function () {
    wx.navigateBack()
  },

  // 提交表单
  submitForm: function () {
    // 验证表单
    if (!this.validateForm()) {
      return false
    }

    // 验证是否同意发布须知
    if (!this.data.isAgreed) {
      wx.showToast({
        title: '请阅读并同意发布规则和交易条款',
        icon: 'none'
      })
      return false
    }

    wx.showLoading({
      title: this.data.isEdit ? '保存中...' : '发布中...',
      mask: true
    })

    // 构建请求数据
    const formData = this.data.formData

    // 获取用户位置信息（从全局数据或用户定位）
    const communityInfo = wx.getStorageSync('selectedCommunity') || {}
    debugger
    const requestData = {
      title: formData.title,
      stuffDescribe: formData.stuffDescribe,
      categoryCode: formData.categoryCode,
      type: formData.type,
      amount: formData.type !== 'free' ? parseFloat(formData.amount) || 0 : 0,
      stock: parseInt(formData.stock) || 1,
      points: parseInt(formData.points) || 0,
      media: formData.media.join(','),
      lng: communityInfo.lng || 0.1,
      lat: communityInfo.lat || 0.1,
      address: formData.address || communityInfo.address || '',
      orderExpireTime: parseInt(formData.orderExpireTime) || 1,
      timeUnit: formData.timeUnit || 'hour'
    }

    // 调用API
    const apiCall = this.data.isEdit ?
      goodsApi.updateMyGoods({ ...requestData, id: this.data.goodsId }) :
      goodsApi.addMyGoods(requestData)

    apiCall.then(res => {
     
        wx.showToast({
          title: this.data.isEdit ? '保存成功' : '发布成功',
          icon: 'success'
        })

        // 返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      
    }).catch(err => {
      console.error('提交失败:', err)

    }).finally(() => {
      wx.hideLoading()
    })
  },



  // 表单验证
  validateForm: function () {
    const formData = this.data.formData

    // 验证标题
    if (!formData.title.trim()) {
      wx.showToast({
        title: '请输入好物名称',
        icon: 'none'
      })
      return false
    }

    // 验证图片
    if (formData.media.length === 0) {
      wx.showToast({
        title: '请上传至少一张商品图片',
        icon: 'none'
      })
      return false
    }

    // 验证描述
    if (!formData.stuffDescribe.trim()) {
      wx.showToast({
        title: '请输入商品描述',
        icon: 'none'
      })
      return false
    }

    // 验证分类
    if (!formData.categoryCode) {
      wx.showToast({
        title: '请选择商品分类',
        icon: 'none'
      })
      return false
    }

    // 验证类型
    if (!formData.type) {
      wx.showToast({
        title: '请选择商品类型',
        icon: 'none'
      })
      return false
    }

    // 验证价格（非免费送）
    if (formData.type !== 'free') {
      if (!formData.amount) {
        wx.showToast({
          title: '请输入商品价格',
          icon: 'none'
        })
        return false
      }

      if (isNaN(parseFloat(formData.amount)) || parseFloat(formData.amount) < 0) {
        wx.showToast({
          title: '请输入有效的商品价格',
          icon: 'none'
        })
        return false
      }
    }

    // 验证库存
    if (!formData.stock || isNaN(parseInt(formData.stock)) || parseInt(formData.stock) <= 0) {
      wx.showToast({
        title: '请输入有效的库存数量',
        icon: 'none'
      })
      return false
    }

    // 验证交易地点
    if (!formData.address.trim()) {
      wx.showToast({
        title: '请输入交易地点',
        icon: 'none'
      })
      return false
    }

    // 验证订单超时时间
    if (!formData.orderExpireTime || isNaN(parseInt(formData.orderExpireTime)) || parseInt(formData.orderExpireTime) <= 0) {
      wx.showToast({
        title: '请输入有效的订单超时时间',
        icon: 'none'
      })
      return false
    }

    // 验证时间单位
    if (!formData.timeUnit) {
      wx.showToast({
        title: '请选择时间单位',
        icon: 'none'
      })
      return false
    }

    return true
  }
})
