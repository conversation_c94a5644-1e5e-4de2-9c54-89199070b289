<!--订单详情页-->
<view class="container {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}" wx:if="{{order}}">
  <!-- 订单状态 -->
  <view class="status-section">
    <view class="status-icon {{order.isExpired ? 'expired' : order.status}}"></view>
    <view class="status-text">
      {{order.isExpired ? '订单已超时' : order.statusName}}
    </view>
    <view class="status-desc">
      {{order.isExpired ? '订单已超过有效期，无法完成交易' :
        order.status === 'pending' ? '请联系卖家完成交易' :
        order.status === 'complete' ? '交易已完成' :
        order.status === 'wait_complete' ? '等待完成交易' : '订单已取消'}}
    </view>
    <view class="expire-time-info" wx:if="{{order.expireTime && !order.isExpired}}">
      <text class="expire-label">过期时间：</text>
      <text class="expire-value">{{order.expireTime}}</text>
    </view>
  </view>

  <!-- 二维码区域 - 仅对买家显示 -->
  <view class="qrcode-section" wx:if="{{searchType === 'buyer' && order.status !== 'complete' && order.status !== 'cancel' && !order.isExpired}}">
    <view class="qrcode-tip">交易二维码</view>
    <view class="qrcode-wrapper">
      <image class="qrcode-image" src="{{imagePath}}" mode="aspectFit" wx:if="{{qrcodeGenerated}}"></image>
      <view class="qrcode-loading" wx:else>
        <view class="loading-text">{{isGeneratingQRCode ? '二维码生成中...' : '二维码生成中...'}}</view>
      </view>
    </view>
    <view class="qrcode-desc">请向卖家出示此二维码完成交易</view>

    <!-- 核销码过期时间提示 -->
    <view class="qrcode-expire-info" wx:if="{{qrCodeExpireTime}}">
      <text class="expire-label">核销码过期时间：</text>
      <text class="expire-value">{{qrCodeExpireTime}}</text>
    </view>

    <!-- 隐藏的canvas用于生成二维码 -->
    <canvas
      style="opacity: 0; position: absolute; left: -1000rpx; width: 422rpx; height: 386rpx;"
      canvas-id="orderCanvas">
    </canvas>
  </view>

  <!-- 超时提示区域 -->
  <view class="expired-section" wx:if="{{order.isExpired}}">
    <view class="expired-icon">⏰</view>
    <view class="expired-title">订单已超时</view>
    <view class="expired-desc">此订单已超过有效期，无法生成核销二维码</view>
    <view class="expired-time" wx:if="{{order.expireTime}}">
      <text class="expire-label">过期时间：</text>
      <text class="expire-value">{{order.expireTime}}</text>
    </view>
  </view>

  <!-- 商品信息 -->
  <view class="goods-section">
    <view class="section-title">商品信息</view>
    <view class="goods-card" bindtap="navigateToGoodsDetail">
      <image class="goods-image" src="{{order.image}}" mode="aspectFill"></image>
      <view class="goods-info">
        <view class="goods-title">{{order.stuffDescribe}}</view>
        <view class="goods-price">¥{{order.unitAmount}} × {{order.quantity}}</view>
        <view class="goods-total">合计: ¥{{order.totalAmount}}</view>
        <view class="order-status">状态: {{order.statusName}}</view>
      </view>
    </view>
  </view>

  <!-- 卖家信息 -->
  <view class="seller-section">
    <view class="section-title">卖家信息</view>
    <view class="info-item">
      <view class="info-label">卖家</view>
      <view class="info-value">{{order.sellerName}}</view>
    </view>
    <view class="info-item">
      <view class="info-label">交易地点</view>
      <view class="info-value">{{order.address}}</view>
    </view>
  </view>

  <!-- 订单信息 -->
  <view class="order-section">
    <view class="section-title">订单信息</view>
    <view class="info-item">
      <view class="info-label">订单编号</view>
      <view class="info-value">{{order.orderNo}}</view>
    </view>
    <view class="info-item">
      <view class="info-label">创建时间</view>
      <view class="info-value">{{order.createTime}}</view>
    </view>
    <view class="info-item" wx:if="{{order.updateTime}}">
      <view class="info-label">更新时间</view>
      <view class="info-value">{{order.updateTime}}</view>
    </view>
    <view class="info-item" wx:if="{{order.phone}}">
      <view class="info-label">联系电话</view>
      <view class="info-value">{{order.phone}}</view>
    </view>
    <view class="info-item" wx:if="{{order.note}}">
      <view class="info-label">备注</view>
      <view class="info-value">{{order.note}}</view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="footer">
    <view class="footer-btn contact" bindtap="contactSeller" wx:if="{{searchType=='buyer'}}">联系卖家</view>
    <view class="footer-btn cancel" bindtap="cancelOrder" wx:if="{{order.status !== 'complete' && order.status !== 'cancel'}}">取消订单</view>
  </view>


</view>
<!-- 加载中 -->
<view class="loading-container" wx:else>
  <view class="loading-icon"></view>
  <view class="loading-text">加载中...</view>
</view>
<!-- 取消订单原因选择弹窗 -->
<view class="modal-overlay" wx:if="{{showCancelModal}}" bindtap="hideCancelModal">
  <view class="cancel-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <view class="modal-title">选择取消原因</view>
      <view class="modal-close" bindtap="hideCancelModal">×</view>
    </view>

    <view class="modal-content">
      <view class="reason-list">
        <!-- 前三个预设原因选项 -->
        <view
          class="reason-item {{selectedCancelReason === item.value ? 'selected' : ''}}"
          wx:for="{{cancelReasons}}"
          wx:for-index="index"
          wx:key="value"
          wx:if="{{index < 3}}"
          catchtap="selectCancelReason"
          data-reason="{{item.value}}">
          <view class="reason-text">{{item.text}}</view>
          <view class="reason-radio">
            <view class="radio-inner {{selectedCancelReason === item.value ? 'selected' : ''}}"></view>
          </view>
        </view>

        <!-- 其他原因选项 -->
        <view class="reason-item custom-reason-item {{selectedCancelReason === 'other' ? 'selected' : ''}}"
              catchtap="selectOtherReason">
          <view class="reason-text">其他原因</view>
          <view class="reason-radio">
            <view class="radio-inner {{selectedCancelReason === 'other' ? 'selected' : ''}}"></view>
          </view>
        </view>
      </view>

      <!-- 自定义原因输入框 -->
      <view class="custom-reason" wx:if="{{selectedCancelReason === 'other'}}">
        <view class="input-label">请详细说明取消原因：</view>
        <textarea
          class="custom-input"
          placeholder="请输入取消原因"
          value="{{customCancelReason}}"
          bindinput="inputCustomReason"
          maxlength="100"
          auto-focus="{{selectedCancelReason === 'other'}}"
          show-confirm-bar="{{false}}">
        </textarea>
      </view>
    </view>

    <view class="modal-footer">
      <view class="modal-btn cancel-btn" bindtap="hideCancelModal">取消</view>
      <view class="modal-btn confirm-btn" bindtap="confirmCancelOrder">确认</view>
    </view>
  </view>
</view>


