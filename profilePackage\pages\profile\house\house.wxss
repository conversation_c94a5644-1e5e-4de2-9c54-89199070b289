/* 我的房屋页面样式 */
page {
  background-color: #f4f5f7;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
  color: #333;
  line-height: 1.5;
  height: 100%;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}



/* 内容区域 */
.content-area {
  flex: 1;
  padding: 32rpx 40rpx;
  box-sizing: border-box;
  height: 100%;
  padding-bottom: 40rpx; /* 添加底部内边距 */
  overflow-y: auto; /* 允许垂直滚动 */
  -webkit-overflow-scrolling: touch; /* iOS 平滑滚动 */
}

/* 房屋列表样式 */
.house-count {
  font-size: 28rpx;
  color: #8e8e93;
  margin-bottom: 32rpx;
  padding-left: 8rpx;
}

.house-list {
  margin-bottom: 40rpx;
}

.house-card {
  background: white;
  border-radius: 32rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: relative;
  transition: transform 0.2s, box-shadow 0.2s;
}

.house-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.03);
}

.house-card.default {
  border: 4rpx solid #007AFF;
}

.house-content {
  padding: 32rpx;
}

.house-address {
  font-size: 34rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  color: #333;
}

.house-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24rpx;
}

.house-role {
  font-size: 28rpx;
  color: #666;
}

.house-status {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

/* 二维码按钮样式 */
.house-qr-button {
  position: absolute;
  right: 32rpx;
  top: 32rpx;
  width: 64rpx;
  height: 64rpx;
  background: rgba(0, 122, 255, 0.1);
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.house-qr-button:active {
  transform: scale(0.95);
  background: rgba(0, 122, 255, 0.2);
}

.qr-icon {
  width: 36rpx;
  height: 36rpx;
  opacity: 0.8;
}

.status-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
  height: 44rpx;
  min-width: 80rpx;
  box-sizing: border-box;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
}

.tag-default {
  background-color: #E1F0FF;
  color: #007AFF;
  margin-right: 6px;
}

.tag-verified {
  background-color: #E3F9E5;
  color: #34C759;
}

.tag-unverified {
  background-color: #F2F2F7;
  color: #8E8E93;
}



/* 添加按钮 */
.add-button-container {
  margin-top: 24px;
  margin-bottom: 16px;
  padding: 0 20px;
}

.add-button {
  width: 100%;
  height: 48px;
  background: #FF9500;
  color: white;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(255, 149, 0, 0.2);
  border: none;
}

.add-button:active {
  transform: scale(0.98);
  box-shadow: 0 1px 4px rgba(255, 149, 0, 0.15);
}

.add-icon {
  margin-right: 4px;
  font-size: 20px;
  line-height: 1;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px 40px;
  text-align: center;
}

.empty-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.house-icon {
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='none' stroke='%238e8e93' stroke-width='1.5' d='M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2z'%3E%3C/path%3E%3Cpolyline fill='none' stroke='%238e8e93' stroke-width='1.5' points='9 22 9 12 15 12 15 22'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.empty-text {
  font-size: 16px;
  color: #8e8e93;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: #c7c7cc;
}

/* 悬浮添加按钮 */
.floating-add-button {
  position: fixed;
  right: 20px;
  bottom: 30px;
  width: 56px;
  height: 56px;
  background: #007AFF;
  border-radius: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3);
  z-index: 1000;
  transition: all 0.2s ease;
}

.floating-add-button:active {
  transform: scale(0.95);
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);
}

.floating-add-button .add-icon {
  color: white;
  font-size: 24px;
  font-weight: 300;
  line-height: 1;
  margin: 0;
}

/* 加载状态样式 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100px 20px;
}

.loading-text {
  font-size: 16px;
  color: #8e8e93;
  margin-top: 20px;
}

/* 加载更多状态样式 */
.load-more-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  margin-top: 10px;
}

.load-more-text {
  font-size: 14px;
  color: #8e8e93;
  text-align: center;
}

/* 底部弹窗样式 */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 2000;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.25s, visibility 0.25s;
}

.popup-overlay.active {
  opacity: 1;
  visibility: visible;
}

.popup-container {
  width: 100%;
  background-color: #ffffff;
  border-radius: 16px 16px 0 0;
  overflow: hidden;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2001;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.popup-overlay.active .popup-container {
  transform: translateY(0);
}

.popup-header {
  padding: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.popup-title {
  font-size: 17px;
  font-weight: 600;
  color: #000;
}

.popup-close {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24px;
  color: #8e8e93;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 15px;
}

.popup-close:active {
  background-color: rgba(0, 0, 0, 0.05);
}

.popup-content {
  padding: 16px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 表单样式 */
.form-description {
  font-size: 14px;
  color: #8e8e93;
  line-height: 1.5;
  margin-bottom: 16px;
}

.form-container {
  background: white;
  border-radius: 16px;
  margin-bottom: 20px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.form-group {
  padding: 16px;
  border-bottom: 0.5px solid rgba(0, 0, 0, 0.05);
}

.form-group:last-child {
  border-bottom: none;
}

.form-label {
  display: block;
  font-size: 14px;
  color: #3a3a3c;
  margin-bottom: 8px;
  font-weight: 500;
}

/* 选择器样式 */
.picker-wrapper {
  position: relative;
}

.picker-content {
  width: 92%;
  height: 44px;
  padding: 0 12px;
  border: 0.5px solid rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  font-size: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: rgba(255, 255, 255, 0.8);
  transition: all 0.2s ease;
}

.picker-content:active {
  background-color: rgba(0, 0, 0, 0.02);
}

.picker-content.placeholder {
  color: #999;
}

.picker-arrow {
  color: #8e8e93;
  font-size: 16px;
}

/* 级联选择器样式 */
.cascade-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 2000;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.25s, visibility 0.25s;
}

.cascade-picker-overlay.active {
  opacity: 1;
  visibility: visible;
}

.cascade-picker-container {
  width: 100%;
  background-color: rgba(248, 248, 248, 0.95);
  border-radius: 12px 12px 0 0;
  overflow: hidden;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2001;
}

.cascade-picker-overlay.active .cascade-picker-container {
  transform: translateY(0);
}

.cascade-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 0.5px solid rgba(0, 0, 0, 0.1);
  background-color: rgba(248, 248, 248, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  flex-wrap: nowrap; /* 防止内容换行 */
}

.cascade-picker-title {
  font-size: 16px;
  font-weight: 600;
  color: #000;
  white-space: nowrap; /* 防止文字换行 */
  min-width: 80px; /* 确保标题有足够的宽度 */
  text-align: center; /* 文字居中 */
}

.cascade-picker-cancel,
.cascade-picker-confirm {
  font-size: 16px;
  color: #007AFF;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  font-weight: 400;
  line-height: 1.5;
}

.cascade-picker-cancel {
  opacity: 0.8;
}

.cascade-picker-content {
  display: flex;
  height: 220px;
  position: relative;
  background-color: rgba(248, 248, 248, 0.95);
}

.cascade-picker-column {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  position: relative;
  scroll-snap-type: y mandatory;
}

.cascade-picker-column:not(:last-child) {
  border-right: 0.5px solid rgba(0, 0, 0, 0.05);
}

.cascade-picker-item {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  font-size: 16px;
  color: #333;
  transition: all 0.15s;
  scroll-snap-align: center;
  position: relative;
}

.cascade-picker-item.active {
  color: #007AFF;
  font-weight: 500;
}

.cascade-picker-item.active::after {
  content: '';
  position: absolute;
  right: 10px;
  width: 18px;
  height: 18px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23007AFF'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  opacity: 0.9;
}

.cascade-picker-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  background: linear-gradient(
      to bottom,
      rgba(248, 248, 248, 0.95) 0%,
      rgba(248, 248, 248, 0.7) 15%,
      rgba(248, 248, 248, 0) 30%,
      rgba(248, 248, 248, 0) 70%,
      rgba(248, 248, 248, 0.7) 85%,
      rgba(248, 248, 248, 0.95) 100%
  );
}

.cascade-picker-indicator {
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  height: 44px;
  transform: translateY(-50%);
  border-top: 0.5px solid rgba(0, 0, 0, 0.1);
  border-bottom: 0.5px solid rgba(0, 0, 0, 0.1);
  pointer-events: none;
  background-color: rgba(0, 122, 255, 0.05);
}

/* 单选按钮组 */
.radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 8px;
}

.radio-option {
  flex: 1;
  min-width: 80px;
  height: 44px;
  border: 0.5px solid rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #3a3a3c;
  background-color: rgba(255, 255, 255, 0.8);
  transition: all 0.2s ease;
}

.radio-option:active {
  transform: scale(0.98);
  background-color: rgba(0, 0, 0, 0.02);
}

.radio-option.selected {
  border-color: #007AFF;
  background-color: rgba(0, 122, 255, 0.08);
  color: #007AFF;
  font-weight: 500;
}

/* 提交按钮样式 */
.submit-button-container {
  margin-top: 24px;
  margin-bottom: 16px;
  padding: 0;
  display: flex;
  justify-content: center;
  width: 100%;
}

/* 在弹窗中的提交按钮容器 */
.popup-content .submit-button-container {
  padding: 0 16px;
}

.submit-button {
  width: 90%;
  max-width: 320px;
  height: 50px;
  background: #007AFF;
  color: white;
  border-radius: 12px;
  border: none;
  font-size: 17px;
  font-weight: 600;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.2);
  letter-spacing: 0.5px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.submit-button:active {
  transform: scale(0.98);
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);
  background: #0071eb;
}

.submit-button.disabled {
  background: rgba(0, 122, 255, 0.3);
  cursor: default;
  box-shadow: none;
}

/* 加载指示器 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.loading-overlay.active {
  opacity: 1;
  visibility: visible;
}

.loading-spinner {
  width: 80px;
  height: 80px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(0, 122, 255, 0.2);
  border-top-color: #007AFF;
  border-radius: 50%;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 8px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 12px;
  color: #8e8e93;
  margin-top: 4px;
}

/* 确认弹窗样式 */
.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.confirm-dialog-overlay.active {
  opacity: 1;
  visibility: visible;
}

.confirm-dialog {
  width: 280px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 14px;
  overflow: hidden;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: scale(0.9);
  transition: transform 0.3s;
}

.confirm-dialog-overlay.active .confirm-dialog {
  transform: scale(1);
}

.confirm-dialog-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.confirm-dialog-icon {
  width: 56px;
  height: 56px;
  background-color: rgba(52, 199, 89, 0.1);
  border-radius: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 12px;
}

.confirm-dialog-title {
  font-size: 17px;
  font-weight: 600;
  color: #000;
  margin-bottom: 8px;
}

.confirm-dialog-message {
  font-size: 14px;
  color: #8e8e93;
  margin-bottom: 16px;
  line-height: 1.4;
}

.confirm-dialog-buttons {
  width: 100%;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
}

.confirm-dialog-button {
  flex: 1;
  padding: 14px 0;
  font-size: 17px;
  font-weight: 500;
  text-align: center;
  transition: background-color 0.2s;
}

.confirm-dialog-button.primary {
  color: #007AFF;
}

.confirm-dialog-button:active {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 新增房屋弹窗样式 */
.add-house-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.add-house-modal.show {
  opacity: 1;
  visibility: visible;
}

.add-house-modal .modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
}

.add-house-modal .modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 16px 16px 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
}

.add-house-modal.show .modal-content {
  transform: translateY(0);
}

.add-house-modal .modal-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.add-house-modal .modal-title {
  font-size: 17px;
  font-weight: 600;
  color: #333;
}

.add-house-modal .model-selected-view{
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
display: flex;
align-items: center;
}

.select-building-view{
  padding:5rpx 15px;
  margin-left: 15rpx;
  border: 1rpx solid #f0f0f0;
}

.add-house-modal .modal-close {
  position: absolute;
  right: 20px;
  font-size: 24px;
  color: #999;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 15px;
}

.add-house-modal .modal-close:active {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 已选择信息显示区域 */
.selection-info {
  padding: 12px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.selection-item-info {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  position: relative;
}

.selection-item-info:last-child {
  margin-bottom: 0;
}

.selection-label {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
  min-width: 70px;
}

.selection-value {
  font-size: 14px;
  color: #007AFF;
  font-weight: 500;
  flex: 1;
}

.back-button {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  background: #007AFF;
  border-radius: 12px;
  margin-left: 12px;
  transition: all 0.2s ease;
}

.back-button:active {
  background: #0056b3;
  transform: scale(0.95);
}

.back-text {
  font-size: 12px;
  color: white;
}

.add-house-modal .modal-body {
  flex: 1;
  padding: 20px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 搜索框样式 */
.search-container {
  margin-bottom: 20px;
}

.search-input {
  width: 100%;
  height: 44px;
  padding: 0 16px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  font-size: 16px;
  box-sizing: border-box;
}

.search-input:focus {
  border-color: #007AFF;
}

/* 内容滚动区域 */
.content-scroll {
  flex: 1;
  margin-bottom: 20px;
  max-height: 200px;
}

/* 小型空状态样式 */
.empty-state-small {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-text-small {
  font-size: 14px;
  color: #999;
}

/* 选择网格布局 */
.selection-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.selection-item {
  flex: 0 0 auto;
  min-width: 110rpx;
  padding: 12px 16px;
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  text-align: center;
  font-size: 14px;
  color: #333;
  transition: all 0.2s ease;
}

.selection-item:active {
  transform: scale(0.98);
}

.selection-item.selected {
  border-color: #007AFF;
  background: rgba(0, 122, 255, 0.1);
  color: #007AFF;
  font-weight: 500;
}



/* 住户身份选择区域 */
.resident-type-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.resident-type-grid {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.resident-type-item {
  
  padding: 12px 16px;
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  text-align: center;
  font-size: 14px;
  color: #333;
  transition: all 0.2s ease;
}

.resident-type-item:active {
  transform: scale(0.98);
}

.resident-type-item.selected {
  border-color: #007AFF;
  background: rgba(0, 122, 255, 0.1);
  color: #007AFF;
  font-weight: 500;
}

/* 弹窗底部操作栏 */
.add-house-modal .modal-footer {
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 12px;
  background: white;
}

.btn-cancel,
.btn-confirm {
  flex: 1;
  height: 44px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
}

.btn-cancel {
  background: #f8f9fa;
  color: #666;
}

.btn-cancel:active {
  background: #e9ecef;
}

.btn-confirm {
  background: #007AFF;
  color: white;
}

.btn-confirm.enabled:active {
  background: #0056b3;
}

.btn-confirm.disabled {
  background: #ccc;
  color: #999;
  opacity: 0.6;
}


/* 查找我的房产按钮样式 */
.search-myhouse {
  position: absolute;
  right: 15rpx;
  top: 20rpx;
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background: #007AFF;
  color: white;
  border-radius: 20rpx;
  font-size: 28rpx;
  transition: all 0.2s ease;
  z-index: 10;
}

.search-myhouse:active {
  background: #0056b3;
  transform: scale(0.98);
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: 8rpx;
  filter: brightness(0) invert(1);
}

/* 查找房产弹窗样式 */
.find-house-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.find-house-modal.show {
  opacity: 1;
  visibility: visible;
}

.find-house-modal .modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.find-house-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 500px;
  max-height: 75vh;
  background: white;
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.find-house-modal.show .find-house-content {
  animation: modalSlideIn 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* 弹窗头部样式 */
.find-house-modal .modal-header {
  padding: 24px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fafafa;
}

/* 住户信息区域样式 */
.resident-info-section {
  padding: 20px 24px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 14px;
  color: #666;
  width: 80px;
  flex-shrink: 0;
}

.info-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.find-house-modal .modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.find-house-modal .modal-close {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: #f5f5f5;
  transition: all 0.2s ease;
}

.find-house-modal .modal-close:active {
  background: #e0e0e0;
  transform: scale(0.95);
}

.close-icon {
  font-size: 20px;
  color: #666;
  line-height: 1;
}

/* 弹窗内容区域 */
.find-house-body {
  flex: 1;
  padding: 20px 24px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 200px;
}

/* 房产表格容器 */
.house-table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

/* 表格头部 */
.table-header {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-weight: 600;
  color: #495057;
}

/* 表格内容滚动区域 */
.table-scroll {
  flex: 1;
  max-height: 300px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 表格行 */
.table-row {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.table-row:hover {
  background-color: #f8f9fa;
}

.table-row.selected {
  background-color: rgba(0, 122, 255, 0.08);
  border-color: #007AFF;
}

.table-row:last-child {
  border-bottom: none;
}

/* 表格单元格 */
.table-cell {
  padding: 12px 8px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  border-right: 1px solid #f0f0f0;
  word-break: break-all;
  line-height: 1.4;
}

.table-cell:last-child {
  border-right: none;
}

.header-cell {
  font-weight: 600;
  color: #495057;
  background: #f8f9fa;
}

/* 列宽设置 */
.building-col {
  flex: 1;
  min-width: 60px;
}

.unit-col {
  flex: 1;
  min-width: 60px;
}

.room-col {
  flex: 1;
  min-width: 60px;
}

.name-col {
  flex: 1.2;
  min-width: 80px;
}

.phone-col {
  flex: 1.5;
  min-width: 100px;
}

.type-col {
  flex: 1.2;
  min-width: 80px;
}

.status-col {
  flex: 1;
  min-width: 70px;
}

/* 类型标签 */
.type-tag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.type-tag.owner {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

/* 状态标签 */
.status-tag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.status-tag.approved {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.status-tag.pending {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.loading-spinner {
  margin-bottom: 16px;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f0f0f0;
  border-top-color: #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.house-list-scroll {
  flex: 1;
  max-height: 350px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  margin: -8px -4px;
  padding: 8px 4px;
}

.find-house-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.find-house-item {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 16px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  cursor: pointer;
}

.find-house-item:active {
  transform: scale(0.98);
}

.find-house-item.selected {
  background: rgba(0, 122, 255, 0.08);
  border-color: #007AFF;
}

.house-info-content {
  flex: 1;
  margin-right: 16px;
}

.house-address {
  font-size: 17px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.3;
}

.house-details {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.house-detail-item {
  display: flex;
  align-items: center;
}

.detail-label {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
  min-width: 40px;
}

.detail-value {
  font-size: 14px;
  color: #333;
}

.house-type {
  color: #007AFF !important;
  background: rgba(0, 122, 255, 0.1);
  padding: 2px 8px;
  border-radius: 6px;
  font-weight: 500;
}

/* 自定义复选框样式 */
.house-checkbox {
  flex-shrink: 0;
}

.checkbox-wrapper {
  width: 24px;
  height: 24px;
  border: 2px solid #ddd;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  transition: all 0.2s ease;
}

.checkbox-wrapper.checked {
  background: #007AFF;
  border-color: #007AFF;
}

.checkbox-icon {
  color: white;
  font-size: 14px;
  font-weight: bold;
  line-height: 1;
}

/* 弹窗空状态样式 */
.empty-state-modal {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
}

.empty-icon-modal {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.6;
}

.empty-text-modal {
  font-size: 17px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.empty-desc-modal {
  font-size: 14px;
  color: #999;
  line-height: 1.5;
}

/* 弹窗底部操作栏样式 */
.find-house-modal .modal-footer {
  padding: 20px 24px 24px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 16px;
  background: #fafafa;
}

.find-house-modal .btn-cancel,
.find-house-modal .btn-confirm {
  flex: 1;
  height: 38px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.find-house-modal .btn-cancel {
  background: #f5f5f5;
  color: #666;
}

.find-house-modal .btn-cancel:active {
  background: #e0e0e0;
  transform: scale(0.98);
}

.find-house-modal .btn-confirm {
  background: #007AFF;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.find-house-modal .btn-confirm.enabled {
  background: linear-gradient(135deg, #007AFF, #0056b3);
}

.find-house-modal .btn-confirm.enabled:active {
  transform: scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 122, 255, 0.2);
}

.find-house-modal .btn-confirm.disabled {
  background: #e9ecef;
  color: #adb5bd;
  box-shadow: none;
  cursor: not-allowed;
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
  .find-house-content {
    background: #2c2c2e;
  }

  .find-house-modal .modal-header {
    background: #3a3a3c;
    border-bottom-color: #48484a;
  }

  .find-house-modal .modal-title {
    color: #f5f5f7;
  }

  .find-house-modal .modal-close {
    background: #48484a;
  }

  .close-icon {
    color: #8e8e93;
  }

  /* 住户信息区域暗黑模式 */
  .resident-info-section {
    background: #3a3a3c;
    border-bottom-color: #48484a;
  }

  .info-label {
    color: #8e8e93;
  }

  .info-value {
    color: #f5f5f7;
  }

  /* 表格暗黑模式 */
  .house-table-container {
    background: #2c2c2e;
    border-color: #48484a;
  }

  .table-header {
    background: #3a3a3c;
    border-bottom-color: #48484a;
  }

  .header-cell {
    color: #f5f5f7;
    background: #3a3a3c;
  }

  .table-row {
    border-bottom-color: #48484a;
  }

  .table-row:hover {
    background-color: #3a3a3c;
  }

  .table-row.selected {
    background: rgba(10, 132, 255, 0.15);
    border-color: #0a84ff;
  }

  .table-cell {
    color: #f5f5f7;
    border-right-color: #48484a;
  }

  .type-tag.owner {
    background: rgba(52, 199, 89, 0.2);
    color: #34c759;
  }

  .status-tag.approved {
    background: rgba(52, 199, 89, 0.2);
    color: #34c759;
  }

  .status-tag.pending {
    background: rgba(255, 214, 10, 0.2);
    color: #ffd60a;
  }

  .empty-text-modal {
    color: #8e8e93;
  }

  .empty-desc-modal {
    color: #636366;
  }

  .find-house-modal .modal-footer {
    background: #3a3a3c;
    border-top-color: #48484a;
  }

  .find-house-modal .btn-cancel {
    background: #48484a;
    color: #8e8e93;
  }

  .find-house-modal .btn-cancel:active {
    background: #636366;
  }

  .find-house-modal .btn-confirm.enabled {
    background: linear-gradient(135deg, #0a84ff, #0056b3);
  }

  .find-house-modal .btn-confirm.disabled {
    background: #48484a;
    color: #636366;
  }
}