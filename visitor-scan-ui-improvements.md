# 访客扫码核销UI优化和数据刷新

## 修改概述
针对物业人员扫码进入访客凭证页面的用户体验进行了优化，隐藏了不必要的UI元素，并在核销成功后增加了数据刷新功能。

## 具体修改内容

### 1. UI元素隐藏优化

#### 隐藏使用提示 (`servicePackage/pages/visitor/credential/index.wxml`)
```xml
<!-- 使用提示 (物业人员扫码进入时隐藏) -->
<view class="visitor-usage-tip {{communityType === 'smart' ? 'smart' : 'normal'}}" wx:if="{{!fromScan}}">
  <image src="{{communityType === 'smart' ? '/images/icons/gate.svg' : '/images/icons/guard.svg'}}" class="usage-tip-icon"></image>
  <text>{{usageTip}}</text>
</view>
```

**隐藏内容**：
- "请向门卫出示此二维码，由门卫扫码核验" 提示
- "请在闸机处亮屏通行，系统将自动识别" 提示

#### 隐藏操作按钮区域
```xml
<!-- 操作图标区 (物业人员扫码进入时隐藏) -->
<view class="visitor-action-icons" wx:if="{{!fromScan}}">
  <!-- 保存、分享、作废、延期等按钮 -->
</view>
```

**隐藏内容**：
- 保存按钮
- 分享按钮  
- 作废按钮
- 延期按钮
- 立即生效按钮

### 2. 核销成功后数据刷新

#### 新增刷新方法 (`servicePackage/pages/visitor/credential/index.js`)
```javascript
// 刷新访客数据
refreshVisitorData: function () {
  console.log('核销成功后刷新访客数据...');
  
  // 重新获取访客详情
  visitorsApi.getVisitorDetail(this.data.visitorId)
    .then(res => {
      if (res) {
        const visitorData = res;
        
        // 计算状态文本
        const statusTextMap = this.data.statusTextMap;
        const status = statusTextMap.filter(item => item.nameEn === visitorData.status);
        
        // 计算过期时间
        const expireDate = dateUtil.addHoursToTime(visitorData.visitTime, visitorData.stayDuration);
        
        // 更新访客数据
        this.setData({
          visitorData: visitorData,
          statusText: status.length > 0 ? status[0].nameCn : '未知状态',
          expireTime: expireDate
        });
      }
    })
    .catch(err => {
      console.error('刷新访客数据失败：', err);
    });
}
```

#### 修改核销成功处理逻辑
```javascript
// 执行核销操作
performVerify: function () {
  visitorsApi.verifyVisitor(this.data.visitorId)
    .then(res => {
      // 核销成功
      this.setData({
        isVerifying: false,
        verifySuccess: true,
        showVerifyButton: false
      });

      wx.showToast({
        title: '核销成功',
        icon: 'success',
        duration: 2000
      });

      // 重新请求详情接口更新数据状态
      this.refreshVisitorData();
    })
    .catch(err => {
      // 错误处理
    });
}
```

## 功能对比

### 普通用户访问（fromScan=false）
- ✅ 显示使用提示
- ✅ 显示保存、分享等操作按钮
- ✅ 完整的访客凭证功能

### 物业人员扫码访问（fromScan=true）
- ❌ 隐藏使用提示（不需要向门卫出示）
- ❌ 隐藏操作按钮（专注于核销功能）
- ✅ 显示核销按钮（满足条件时）
- ✅ 核销成功后自动刷新数据

## 用户体验改进

### 1. 界面简化
- **减少干扰**：隐藏不相关的提示和按钮
- **突出重点**：核销按钮成为页面焦点
- **专业体验**：为物业人员提供专门的操作界面

### 2. 数据准确性
- **实时更新**：核销后立即获取最新状态
- **状态同步**：确保页面显示与服务器数据一致
- **错误处理**：刷新失败不影响核销成功提示

### 3. 操作流程优化
```
扫码 → 进入页面 → 显示核销按钮 → 执行核销 → 刷新数据 → 显示最新状态
```

## 技术实现细节

### 条件渲染逻辑
- 使用 `wx:if="{{!fromScan}}"` 控制元素显示
- 基于 `fromScan` 参数判断访问来源
- 保持向后兼容，不影响现有功能

### 数据刷新机制
- 核销成功后立即调用详情接口
- 重新计算状态文本和过期时间
- 更新页面数据显示
- 异常处理不影响用户体验

### 状态管理
- 保持核销成功状态 `verifySuccess: true`
- 隐藏核销按钮 `showVerifyButton: false`
- 更新访客状态为最新数据

## 测试场景

### 场景1：物业人员扫码核销
1. 物业人员扫描访客二维码
2. 进入简化版访客凭证页面
3. 不显示使用提示和操作按钮
4. 显示核销按钮并执行核销
5. 核销成功后自动刷新数据
6. 显示最新的访客状态

### 场景2：普通用户访问
1. 用户通过其他方式进入访客凭证页面
2. 显示完整的页面内容
3. 包含使用提示和所有操作按钮
4. 保持原有功能不变

### 场景3：数据刷新异常
1. 核销成功但刷新数据失败
2. 仍然显示核销成功提示
3. 页面状态保持核销成功状态
4. 不影响用户体验

## 优化效果

1. **界面更专业**：物业人员看到的是专门的核销界面
2. **操作更专注**：去除干扰元素，突出核销功能
3. **数据更准确**：核销后立即同步最新状态
4. **体验更流畅**：简化的界面和自动刷新机制

这些优化使得访客扫码核销功能更加专业和用户友好，为物业人员提供了更好的操作体验。
